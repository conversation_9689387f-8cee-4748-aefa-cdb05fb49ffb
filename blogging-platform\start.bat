@echo off
echo Starting MERN Stack Blogging Platform...
echo.

echo Starting MongoDB (make sure MongoDB is installed and running)
echo.

echo Starting Backend Server...
start "Backend Server" cmd /k "cd backend && npm run dev"

echo Waiting for backend to start...
timeout /t 5 /nobreak > nul

echo Starting Frontend React App...
start "Frontend React App" cmd /k "cd frontend && npm start"

echo.
echo ========================================
echo   MERN Stack Blogging Platform Started
echo ========================================
echo.
echo Backend API: http://localhost:5000
echo Frontend App: http://localhost:3000
echo.
echo Press any key to exit...
pause > nul
