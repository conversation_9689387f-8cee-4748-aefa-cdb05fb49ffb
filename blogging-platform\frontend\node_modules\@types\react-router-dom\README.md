# Installation
> `npm install --save @types/react-router-dom`

# Summary
This package contains type definitions for react-router-dom (https://github.com/ReactTraining/react-router).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-router-dom.

### Additional Details
 * Last updated: <PERSON><PERSON>, 18 Jan 2022 22:01:52 GMT
 * Dependencies: [@types/react-router](https://npmjs.com/package/@types/react-router), [@types/react](https://npmjs.com/package/@types/react), [@types/history](https://npmjs.com/package/@types/history)
 * Global values: none

# Credits
These definitions were written by [<PERSON><PERSON>](https://github.com/huy-nguyen), [<PERSON>](https://github.com/p-jackson), [<PERSON>](https://github.com/johnny<PERSON>illy), [<PERSON>](https://github.com/eps1lon), [<PERSON>](https://github.com/danielnixon), [<PERSON>](https://github.com/ynotdraw), and [<PERSON><PERSON><PERSON>](https://github.com/1pete).
