{"ast": null, "code": "var _jsxFileName = \"D:\\\\D Drive\\\\Projects\\\\Blogging\\\\blogging-platform\\\\frontend\\\\src\\\\components\\\\Footer.tsx\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Footer = () => {\n  const currentYear = new Date().getFullYear();\n  const footerLinks = {\n    platform: [{\n      label: 'Home',\n      path: '/'\n    }, {\n      label: 'Blogs',\n      path: '/blogs'\n    }, {\n      label: 'About',\n      path: '/about'\n    }, {\n      label: 'Contact',\n      path: '/contact'\n    }],\n    categories: [{\n      label: 'Technology',\n      path: '/blogs?category=Technology'\n    }, {\n      label: 'Lifestyle',\n      path: '/blogs?category=Lifestyle'\n    }, {\n      label: 'Travel',\n      path: '/blogs?category=Travel'\n    }, {\n      label: 'Business',\n      path: '/blogs?category=Business'\n    }],\n    support: [{\n      label: 'Help Center',\n      path: '/help'\n    }, {\n      label: 'Privacy Policy',\n      path: '/privacy'\n    }, {\n      label: 'Terms of Service',\n      path: '/terms'\n    }, {\n      label: 'Guidelines',\n      path: '/guidelines'\n    }]\n  };\n  const socialLinks = [{\n    name: 'Twitter',\n    href: '#',\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      className: \"w-5 h-5\",\n      fill: \"currentColor\",\n      viewBox: \"0 0 24 24\",\n      children: /*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 9\n    }, this)\n  }, {\n    name: 'GitHub',\n    href: '#',\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      className: \"w-5 h-5\",\n      fill: \"currentColor\",\n      viewBox: \"0 0 24 24\",\n      children: /*#__PURE__*/_jsxDEV(\"path\", {\n        fillRule: \"evenodd\",\n        d: \"M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z\",\n        clipRule: \"evenodd\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 9\n    }, this)\n  }, {\n    name: 'LinkedIn',\n    href: '#',\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      className: \"w-5 h-5\",\n      fill: \"currentColor\",\n      viewBox: \"0 0 24 24\",\n      children: /*#__PURE__*/_jsxDEV(\"path\", {\n        fillRule: \"evenodd\",\n        d: \"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\",\n        clipRule: \"evenodd\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 9\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"footer\", {\n    className: \"bg-secondary-900 text-white\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-white font-bold text-lg\",\n                children: \"B\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 66,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"ml-2 text-xl font-bold\",\n              children: \"BlogPlatform\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 68,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-secondary-300 text-sm mb-6\",\n            children: \"A modern blogging platform where writers share their stories and readers discover amazing content.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex space-x-4\",\n            children: socialLinks.map(social => /*#__PURE__*/_jsxDEV(\"a\", {\n              href: social.href,\n              className: \"text-secondary-400 hover:text-white transition-colors duration-200\",\n              \"aria-label\": social.name,\n              children: social.icon\n            }, social.name, false, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold mb-4\",\n            children: \"Platform\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"space-y-2\",\n            children: footerLinks.platform.map(link => /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: link.path,\n                className: \"text-secondary-300 hover:text-white transition-colors duration-200 text-sm\",\n                children: link.label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 93,\n                columnNumber: 19\n              }, this)\n            }, link.path, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold mb-4\",\n            children: \"Categories\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"space-y-2\",\n            children: footerLinks.categories.map(link => /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: link.path,\n                className: \"text-secondary-300 hover:text-white transition-colors duration-200 text-sm\",\n                children: link.label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 110,\n                columnNumber: 19\n              }, this)\n            }, link.path, false, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold mb-4\",\n            children: \"Support\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"space-y-2\",\n            children: footerLinks.support.map(link => /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: link.path,\n                className: \"text-secondary-300 hover:text-white transition-colors duration-200 text-sm\",\n                children: link.label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 19\n              }, this)\n            }, link.path, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"border-t border-secondary-700 mt-8 pt-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-md\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold mb-2\",\n            children: \"Stay Updated\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-secondary-300 text-sm mb-4\",\n            children: \"Subscribe to our newsletter for the latest blog posts and updates.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            className: \"flex\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"email\",\n              placeholder: \"Enter your email\",\n              className: \"flex-1 px-4 py-2 bg-secondary-800 border border-secondary-600 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent text-white placeholder-secondary-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"px-6 py-2 bg-primary-600 hover:bg-primary-700 rounded-r-lg transition-colors duration-200 font-medium\",\n              children: \"Subscribe\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"border-t border-secondary-700 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-secondary-400 text-sm\",\n          children: [\"\\xA9 \", currentYear, \" BlogPlatform. All rights reserved.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex space-x-6 mt-4 md:mt-0\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/privacy\",\n            className: \"text-secondary-400 hover:text-white text-sm transition-colors duration-200\",\n            children: \"Privacy Policy\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/terms\",\n            className: \"text-secondary-400 hover:text-white text-sm transition-colors duration-200\",\n            children: \"Terms of Service\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/cookies\",\n            className: \"text-secondary-400 hover:text-white text-sm transition-colors duration-200\",\n            children: \"Cookie Policy\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 59,\n    columnNumber: 5\n  }, this);\n};\n_c = Footer;\nexport default Footer;\nvar _c;\n$RefreshReg$(_c, \"Footer\");", "map": {"version": 3, "names": ["React", "Link", "jsxDEV", "_jsxDEV", "Footer", "currentYear", "Date", "getFullYear", "footerLinks", "platform", "label", "path", "categories", "support", "socialLinks", "name", "href", "icon", "className", "fill", "viewBox", "children", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fillRule", "clipRule", "map", "social", "link", "to", "type", "placeholder", "_c", "$RefreshReg$"], "sources": ["D:/D Drive/Projects/Blogging/blogging-platform/frontend/src/components/Footer.tsx"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\n\nconst Footer: React.FC = () => {\n  const currentYear = new Date().getFullYear();\n\n  const footerLinks = {\n    platform: [\n      { label: 'Home', path: '/' },\n      { label: 'Blogs', path: '/blogs' },\n      { label: 'About', path: '/about' },\n      { label: 'Contact', path: '/contact' },\n    ],\n    categories: [\n      { label: 'Technology', path: '/blogs?category=Technology' },\n      { label: 'Lifestyle', path: '/blogs?category=Lifestyle' },\n      { label: 'Travel', path: '/blogs?category=Travel' },\n      { label: 'Business', path: '/blogs?category=Business' },\n    ],\n    support: [\n      { label: 'Help Center', path: '/help' },\n      { label: 'Privacy Policy', path: '/privacy' },\n      { label: 'Terms of Service', path: '/terms' },\n      { label: 'Guidelines', path: '/guidelines' },\n    ],\n  };\n\n  const socialLinks = [\n    {\n      name: 'Twitter',\n      href: '#',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path d=\"M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84\" />\n        </svg>\n      ),\n    },\n    {\n      name: 'GitHub',\n      href: '#',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path fillRule=\"evenodd\" d=\"M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z\" clipRule=\"evenodd\" />\n        </svg>\n      ),\n    },\n    {\n      name: 'LinkedIn',\n      href: '#',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path fillRule=\"evenodd\" d=\"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\" clipRule=\"evenodd\" />\n        </svg>\n      ),\n    },\n  ];\n\n  return (\n    <footer className=\"bg-secondary-900 text-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {/* Brand Section */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"flex items-center mb-4\">\n              <div className=\"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-lg\">B</span>\n              </div>\n              <span className=\"ml-2 text-xl font-bold\">BlogPlatform</span>\n            </div>\n            <p className=\"text-secondary-300 text-sm mb-6\">\n              A modern blogging platform where writers share their stories and readers discover amazing content.\n            </p>\n            <div className=\"flex space-x-4\">\n              {socialLinks.map((social) => (\n                <a\n                  key={social.name}\n                  href={social.href}\n                  className=\"text-secondary-400 hover:text-white transition-colors duration-200\"\n                  aria-label={social.name}\n                >\n                  {social.icon}\n                </a>\n              ))}\n            </div>\n          </div>\n\n          {/* Platform Links */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Platform</h3>\n            <ul className=\"space-y-2\">\n              {footerLinks.platform.map((link) => (\n                <li key={link.path}>\n                  <Link\n                    to={link.path}\n                    className=\"text-secondary-300 hover:text-white transition-colors duration-200 text-sm\"\n                  >\n                    {link.label}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Categories */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Categories</h3>\n            <ul className=\"space-y-2\">\n              {footerLinks.categories.map((link) => (\n                <li key={link.path}>\n                  <Link\n                    to={link.path}\n                    className=\"text-secondary-300 hover:text-white transition-colors duration-200 text-sm\"\n                  >\n                    {link.label}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Support */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Support</h3>\n            <ul className=\"space-y-2\">\n              {footerLinks.support.map((link) => (\n                <li key={link.path}>\n                  <Link\n                    to={link.path}\n                    className=\"text-secondary-300 hover:text-white transition-colors duration-200 text-sm\"\n                  >\n                    {link.label}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n        </div>\n\n        {/* Newsletter Subscription */}\n        <div className=\"border-t border-secondary-700 mt-8 pt-8\">\n          <div className=\"max-w-md\">\n            <h3 className=\"text-lg font-semibold mb-2\">Stay Updated</h3>\n            <p className=\"text-secondary-300 text-sm mb-4\">\n              Subscribe to our newsletter for the latest blog posts and updates.\n            </p>\n            <form className=\"flex\">\n              <input\n                type=\"email\"\n                placeholder=\"Enter your email\"\n                className=\"flex-1 px-4 py-2 bg-secondary-800 border border-secondary-600 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent text-white placeholder-secondary-400\"\n              />\n              <button\n                type=\"submit\"\n                className=\"px-6 py-2 bg-primary-600 hover:bg-primary-700 rounded-r-lg transition-colors duration-200 font-medium\"\n              >\n                Subscribe\n              </button>\n            </form>\n          </div>\n        </div>\n\n        {/* Bottom Section */}\n        <div className=\"border-t border-secondary-700 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center\">\n          <p className=\"text-secondary-400 text-sm\">\n            © {currentYear} BlogPlatform. All rights reserved.\n          </p>\n          <div className=\"flex space-x-6 mt-4 md:mt-0\">\n            <Link\n              to=\"/privacy\"\n              className=\"text-secondary-400 hover:text-white text-sm transition-colors duration-200\"\n            >\n              Privacy Policy\n            </Link>\n            <Link\n              to=\"/terms\"\n              className=\"text-secondary-400 hover:text-white text-sm transition-colors duration-200\"\n            >\n              Terms of Service\n            </Link>\n            <Link\n              to=\"/cookies\"\n              className=\"text-secondary-400 hover:text-white text-sm transition-colors duration-200\"\n            >\n              Cookie Policy\n            </Link>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,MAAgB,GAAGA,CAAA,KAAM;EAC7B,MAAMC,WAAW,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;EAE5C,MAAMC,WAAW,GAAG;IAClBC,QAAQ,EAAE,CACR;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAI,CAAC,EAC5B;MAAED,KAAK,EAAE,OAAO;MAAEC,IAAI,EAAE;IAAS,CAAC,EAClC;MAAED,KAAK,EAAE,OAAO;MAAEC,IAAI,EAAE;IAAS,CAAC,EAClC;MAAED,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAW,CAAC,CACvC;IACDC,UAAU,EAAE,CACV;MAAEF,KAAK,EAAE,YAAY;MAAEC,IAAI,EAAE;IAA6B,CAAC,EAC3D;MAAED,KAAK,EAAE,WAAW;MAAEC,IAAI,EAAE;IAA4B,CAAC,EACzD;MAAED,KAAK,EAAE,QAAQ;MAAEC,IAAI,EAAE;IAAyB,CAAC,EACnD;MAAED,KAAK,EAAE,UAAU;MAAEC,IAAI,EAAE;IAA2B,CAAC,CACxD;IACDE,OAAO,EAAE,CACP;MAAEH,KAAK,EAAE,aAAa;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACvC;MAAED,KAAK,EAAE,gBAAgB;MAAEC,IAAI,EAAE;IAAW,CAAC,EAC7C;MAAED,KAAK,EAAE,kBAAkB;MAAEC,IAAI,EAAE;IAAS,CAAC,EAC7C;MAAED,KAAK,EAAE,YAAY;MAAEC,IAAI,EAAE;IAAc,CAAC;EAEhD,CAAC;EAED,MAAMG,WAAW,GAAG,CAClB;IACEC,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE,GAAG;IACTC,IAAI,eACFd,OAAA;MAAKe,SAAS,EAAC,SAAS;MAACC,IAAI,EAAC,cAAc;MAACC,OAAO,EAAC,WAAW;MAAAC,QAAA,eAC9DlB,OAAA;QAAMmB,CAAC,EAAC;MAAya;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjb;EAET,CAAC,EACD;IACEX,IAAI,EAAE,QAAQ;IACdC,IAAI,EAAE,GAAG;IACTC,IAAI,eACFd,OAAA;MAAKe,SAAS,EAAC,SAAS;MAACC,IAAI,EAAC,cAAc;MAACC,OAAO,EAAC,WAAW;MAAAC,QAAA,eAC9DlB,OAAA;QAAMwB,QAAQ,EAAC,SAAS;QAACL,CAAC,EAAC,ktBAAktB;QAACM,QAAQ,EAAC;MAAS;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChwB;EAET,CAAC,EACD;IACEX,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,GAAG;IACTC,IAAI,eACFd,OAAA;MAAKe,SAAS,EAAC,SAAS;MAACC,IAAI,EAAC,cAAc;MAACC,OAAO,EAAC,WAAW;MAAAC,QAAA,eAC9DlB,OAAA;QAAMwB,QAAQ,EAAC,SAAS;QAACL,CAAC,EAAC,ofAAof;QAACM,QAAQ,EAAC;MAAS;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACliB;EAET,CAAC,CACF;EAED,oBACEvB,OAAA;IAAQe,SAAS,EAAC,6BAA6B;IAAAG,QAAA,eAC7ClB,OAAA;MAAKe,SAAS,EAAC,8CAA8C;MAAAG,QAAA,gBAC3DlB,OAAA;QAAKe,SAAS,EAAC,sDAAsD;QAAAG,QAAA,gBAEnElB,OAAA;UAAKe,SAAS,EAAC,eAAe;UAAAG,QAAA,gBAC5BlB,OAAA;YAAKe,SAAS,EAAC,wBAAwB;YAAAG,QAAA,gBACrClB,OAAA;cAAKe,SAAS,EAAC,oEAAoE;cAAAG,QAAA,eACjFlB,OAAA;gBAAMe,SAAS,EAAC,8BAA8B;gBAAAG,QAAA,EAAC;cAAC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eACNvB,OAAA;cAAMe,SAAS,EAAC,wBAAwB;cAAAG,QAAA,EAAC;YAAY;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,eACNvB,OAAA;YAAGe,SAAS,EAAC,iCAAiC;YAAAG,QAAA,EAAC;UAE/C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJvB,OAAA;YAAKe,SAAS,EAAC,gBAAgB;YAAAG,QAAA,EAC5BP,WAAW,CAACe,GAAG,CAAEC,MAAM,iBACtB3B,OAAA;cAEEa,IAAI,EAAEc,MAAM,CAACd,IAAK;cAClBE,SAAS,EAAC,oEAAoE;cAC9E,cAAYY,MAAM,CAACf,IAAK;cAAAM,QAAA,EAEvBS,MAAM,CAACb;YAAI,GALPa,MAAM,CAACf,IAAI;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAMf,CACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNvB,OAAA;UAAAkB,QAAA,gBACElB,OAAA;YAAIe,SAAS,EAAC,4BAA4B;YAAAG,QAAA,EAAC;UAAQ;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxDvB,OAAA;YAAIe,SAAS,EAAC,WAAW;YAAAG,QAAA,EACtBb,WAAW,CAACC,QAAQ,CAACoB,GAAG,CAAEE,IAAI,iBAC7B5B,OAAA;cAAAkB,QAAA,eACElB,OAAA,CAACF,IAAI;gBACH+B,EAAE,EAAED,IAAI,CAACpB,IAAK;gBACdO,SAAS,EAAC,4EAA4E;gBAAAG,QAAA,EAErFU,IAAI,CAACrB;cAAK;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP;YAAC,GANAK,IAAI,CAACpB,IAAI;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOd,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGNvB,OAAA;UAAAkB,QAAA,gBACElB,OAAA;YAAIe,SAAS,EAAC,4BAA4B;YAAAG,QAAA,EAAC;UAAU;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1DvB,OAAA;YAAIe,SAAS,EAAC,WAAW;YAAAG,QAAA,EACtBb,WAAW,CAACI,UAAU,CAACiB,GAAG,CAAEE,IAAI,iBAC/B5B,OAAA;cAAAkB,QAAA,eACElB,OAAA,CAACF,IAAI;gBACH+B,EAAE,EAAED,IAAI,CAACpB,IAAK;gBACdO,SAAS,EAAC,4EAA4E;gBAAAG,QAAA,EAErFU,IAAI,CAACrB;cAAK;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP;YAAC,GANAK,IAAI,CAACpB,IAAI;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOd,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGNvB,OAAA;UAAAkB,QAAA,gBACElB,OAAA;YAAIe,SAAS,EAAC,4BAA4B;YAAAG,QAAA,EAAC;UAAO;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvDvB,OAAA;YAAIe,SAAS,EAAC,WAAW;YAAAG,QAAA,EACtBb,WAAW,CAACK,OAAO,CAACgB,GAAG,CAAEE,IAAI,iBAC5B5B,OAAA;cAAAkB,QAAA,eACElB,OAAA,CAACF,IAAI;gBACH+B,EAAE,EAAED,IAAI,CAACpB,IAAK;gBACdO,SAAS,EAAC,4EAA4E;gBAAAG,QAAA,EAErFU,IAAI,CAACrB;cAAK;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP;YAAC,GANAK,IAAI,CAACpB,IAAI;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOd,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNvB,OAAA;QAAKe,SAAS,EAAC,yCAAyC;QAAAG,QAAA,eACtDlB,OAAA;UAAKe,SAAS,EAAC,UAAU;UAAAG,QAAA,gBACvBlB,OAAA;YAAIe,SAAS,EAAC,4BAA4B;YAAAG,QAAA,EAAC;UAAY;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5DvB,OAAA;YAAGe,SAAS,EAAC,iCAAiC;YAAAG,QAAA,EAAC;UAE/C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJvB,OAAA;YAAMe,SAAS,EAAC,MAAM;YAAAG,QAAA,gBACpBlB,OAAA;cACE8B,IAAI,EAAC,OAAO;cACZC,WAAW,EAAC,kBAAkB;cAC9BhB,SAAS,EAAC;YAAiM;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5M,CAAC,eACFvB,OAAA;cACE8B,IAAI,EAAC,QAAQ;cACbf,SAAS,EAAC,uGAAuG;cAAAG,QAAA,EAClH;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNvB,OAAA;QAAKe,SAAS,EAAC,gGAAgG;QAAAG,QAAA,gBAC7GlB,OAAA;UAAGe,SAAS,EAAC,4BAA4B;UAAAG,QAAA,GAAC,OACtC,EAAChB,WAAW,EAAC,qCACjB;QAAA;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJvB,OAAA;UAAKe,SAAS,EAAC,6BAA6B;UAAAG,QAAA,gBAC1ClB,OAAA,CAACF,IAAI;YACH+B,EAAE,EAAC,UAAU;YACbd,SAAS,EAAC,4EAA4E;YAAAG,QAAA,EACvF;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPvB,OAAA,CAACF,IAAI;YACH+B,EAAE,EAAC,QAAQ;YACXd,SAAS,EAAC,4EAA4E;YAAAG,QAAA,EACvF;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPvB,OAAA,CAACF,IAAI;YACH+B,EAAE,EAAC,UAAU;YACbd,SAAS,EAAC,4EAA4E;YAAAG,QAAA,EACvF;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACS,EAAA,GA3LI/B,MAAgB;AA6LtB,eAAeA,MAAM;AAAC,IAAA+B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}