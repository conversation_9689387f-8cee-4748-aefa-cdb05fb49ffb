[{"D:\\D Drive\\Projects\\Blogging\\blogging-platform\\frontend\\src\\index.tsx": "1", "D:\\D Drive\\Projects\\Blogging\\blogging-platform\\frontend\\src\\reportWebVitals.ts": "2", "D:\\D Drive\\Projects\\Blogging\\blogging-platform\\frontend\\src\\App.tsx": "3", "D:\\D Drive\\Projects\\Blogging\\blogging-platform\\frontend\\src\\components\\Footer.tsx": "4", "D:\\D Drive\\Projects\\Blogging\\blogging-platform\\frontend\\src\\components\\Header.tsx": "5", "D:\\D Drive\\Projects\\Blogging\\blogging-platform\\frontend\\src\\components\\ProtectedRoute.tsx": "6", "D:\\D Drive\\Projects\\Blogging\\blogging-platform\\frontend\\src\\pages\\EditBlog.tsx": "7", "D:\\D Drive\\Projects\\Blogging\\blogging-platform\\frontend\\src\\pages\\BlogList.tsx": "8", "D:\\D Drive\\Projects\\Blogging\\blogging-platform\\frontend\\src\\pages\\Register.tsx": "9", "D:\\D Drive\\Projects\\Blogging\\blogging-platform\\frontend\\src\\pages\\Login.tsx": "10", "D:\\D Drive\\Projects\\Blogging\\blogging-platform\\frontend\\src\\pages\\CreateBlog.tsx": "11", "D:\\D Drive\\Projects\\Blogging\\blogging-platform\\frontend\\src\\pages\\BlogDetail.tsx": "12", "D:\\D Drive\\Projects\\Blogging\\blogging-platform\\frontend\\src\\pages\\UserProfile.tsx": "13", "D:\\D Drive\\Projects\\Blogging\\blogging-platform\\frontend\\src\\pages\\Profile.tsx": "14", "D:\\D Drive\\Projects\\Blogging\\blogging-platform\\frontend\\src\\pages\\Home.tsx": "15", "D:\\D Drive\\Projects\\Blogging\\blogging-platform\\frontend\\src\\pages\\NotFound.tsx": "16", "D:\\D Drive\\Projects\\Blogging\\blogging-platform\\frontend\\src\\pages\\Dashboard.tsx": "17", "D:\\D Drive\\Projects\\Blogging\\blogging-platform\\frontend\\src\\context\\AuthContext.tsx": "18", "D:\\D Drive\\Projects\\Blogging\\blogging-platform\\frontend\\src\\components\\Loading.tsx": "19", "D:\\D Drive\\Projects\\Blogging\\blogging-platform\\frontend\\src\\utils\\helpers.ts": "20", "D:\\D Drive\\Projects\\Blogging\\blogging-platform\\frontend\\src\\services\\api.ts": "21"}, {"size": 554, "mtime": 1748889518703, "results": "22", "hashOfConfig": "23"}, {"size": 425, "mtime": 1748889517323, "results": "24", "hashOfConfig": "23"}, {"size": 2663, "mtime": 1748889888463, "results": "25", "hashOfConfig": "23"}, {"size": 8265, "mtime": 1748889846730, "results": "26", "hashOfConfig": "23"}, {"size": 10919, "mtime": 1748889816842, "results": "27", "hashOfConfig": "23"}, {"size": 926, "mtime": 1748889902410, "results": "28", "hashOfConfig": "23"}, {"size": 781, "mtime": 1748890186478, "results": "29", "hashOfConfig": "23"}, {"size": 16464, "mtime": 1748890082123, "results": "30", "hashOfConfig": "23"}, {"size": 12218, "mtime": 1748890035002, "results": "31", "hashOfConfig": "23"}, {"size": 5789, "mtime": 1748889958284, "results": "32", "hashOfConfig": "23"}, {"size": 698, "mtime": 1748890178317, "results": "33", "hashOfConfig": "23"}, {"size": 15744, "mtime": 1748890129544, "results": "34", "hashOfConfig": "23"}, {"size": 817, "mtime": 1748890195403, "results": "35", "hashOfConfig": "23"}, {"size": 2778, "mtime": 1748890168810, "results": "36", "hashOfConfig": "23"}, {"size": 11622, "mtime": 1748889935113, "results": "37", "hashOfConfig": "23"}, {"size": 1658, "mtime": 1748890205201, "results": "38", "hashOfConfig": "23"}, {"size": 5211, "mtime": 1748890151008, "results": "39", "hashOfConfig": "23"}, {"size": 5809, "mtime": 1748889759545, "results": "40", "hashOfConfig": "23"}, {"size": 5600, "mtime": 1748889871974, "results": "41", "hashOfConfig": "23"}, {"size": 6894, "mtime": 1748889786980, "results": "42", "hashOfConfig": "23"}, {"size": 8509, "mtime": 1748889739723, "results": "43", "hashOfConfig": "23"}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1xlb7oq", {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\D Drive\\Projects\\Blogging\\blogging-platform\\frontend\\src\\index.tsx", [], [], "D:\\D Drive\\Projects\\Blogging\\blogging-platform\\frontend\\src\\reportWebVitals.ts", [], [], "D:\\D Drive\\Projects\\Blogging\\blogging-platform\\frontend\\src\\App.tsx", [], [], "D:\\D Drive\\Projects\\Blogging\\blogging-platform\\frontend\\src\\components\\Footer.tsx", [], [], "D:\\D Drive\\Projects\\Blogging\\blogging-platform\\frontend\\src\\components\\Header.tsx", [], [], "D:\\D Drive\\Projects\\Blogging\\blogging-platform\\frontend\\src\\components\\ProtectedRoute.tsx", [], [], "D:\\D Drive\\Projects\\Blogging\\blogging-platform\\frontend\\src\\pages\\EditBlog.tsx", [], [], "D:\\D Drive\\Projects\\Blogging\\blogging-platform\\frontend\\src\\pages\\BlogList.tsx", ["107", "108"], [], "D:\\D Drive\\Projects\\Blogging\\blogging-platform\\frontend\\src\\pages\\Register.tsx", [], [], "D:\\D Drive\\Projects\\Blogging\\blogging-platform\\frontend\\src\\pages\\Login.tsx", [], [], "D:\\D Drive\\Projects\\Blogging\\blogging-platform\\frontend\\src\\pages\\CreateBlog.tsx", [], [], "D:\\D Drive\\Projects\\Blogging\\blogging-platform\\frontend\\src\\pages\\BlogDetail.tsx", [], [], "D:\\D Drive\\Projects\\Blogging\\blogging-platform\\frontend\\src\\pages\\UserProfile.tsx", [], [], "D:\\D Drive\\Projects\\Blogging\\blogging-platform\\frontend\\src\\pages\\Profile.tsx", [], [], "D:\\D Drive\\Projects\\Blogging\\blogging-platform\\frontend\\src\\pages\\Home.tsx", ["109"], [], "D:\\D Drive\\Projects\\Blogging\\blogging-platform\\frontend\\src\\pages\\NotFound.tsx", [], [], "D:\\D Drive\\Projects\\Blogging\\blogging-platform\\frontend\\src\\pages\\Dashboard.tsx", [], [], "D:\\D Drive\\Projects\\Blogging\\blogging-platform\\frontend\\src\\context\\AuthContext.tsx", ["110", "111", "112"], [], "D:\\D Drive\\Projects\\Blogging\\blogging-platform\\frontend\\src\\components\\Loading.tsx", [], [], "D:\\D Drive\\Projects\\Blogging\\blogging-platform\\frontend\\src\\utils\\helpers.ts", [], [], "D:\\D Drive\\Projects\\Blogging\\blogging-platform\\frontend\\src\\services\\api.ts", [], [], {"ruleId": "113", "severity": 1, "message": "114", "line": 7, "column": 8, "nodeType": "115", "messageId": "116", "endLine": 7, "endColumn": 15}, {"ruleId": "117", "severity": 1, "message": "118", "line": 64, "column": 6, "nodeType": "119", "endLine": 64, "endColumn": 20, "suggestions": "120"}, {"ruleId": "113", "severity": 1, "message": "114", "line": 7, "column": 8, "nodeType": "115", "messageId": "116", "endLine": 7, "endColumn": 15}, {"ruleId": "113", "severity": 1, "message": "121", "line": 175, "column": 13, "nodeType": "115", "messageId": "116", "endLine": 175, "endColumn": 21}, {"ruleId": "113", "severity": 1, "message": "121", "line": 185, "column": 13, "nodeType": "115", "messageId": "116", "endLine": 185, "endColumn": 21}, {"ruleId": "113", "severity": 1, "message": "122", "line": 191, "column": 9, "nodeType": "115", "messageId": "116", "endLine": 191, "endColumn": 19}, "@typescript-eslint/no-unused-vars", "'Loading' is defined but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'currentCategory', 'currentPage', 'sortBy', and 'sortOrder'. Either include them or remove the dependency array.", "ArrayExpression", ["123"], "'apiError' is assigned a value but never used.", "'clearError' is assigned a value but never used.", {"desc": "124", "fix": "125"}, "Update the dependencies array to be: [currentCategory, currentPage, searchParams, sortBy, sortOrder]", {"range": "126", "text": "127"}, [2320, 2334], "[currentCategory, currentPage, searchParams, sortBy, sortOrder]"]