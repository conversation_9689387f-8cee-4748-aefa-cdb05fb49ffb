{"ast": null, "code": "var _jsxFileName = \"D:\\\\D Drive\\\\Projects\\\\Blogging\\\\blogging-platform\\\\frontend\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport Loading from './Loading';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ProtectedRoute = ({\n  children,\n  adminOnly = false\n}) => {\n  _s();\n  const {\n    isAuthenticated,\n    user,\n    isLoading\n  } = useAuth();\n  const location = useLocation();\n\n  // Show loading while checking authentication\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(Loading, {\n      fullScreen: true,\n      text: \"Checking authentication...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 12\n    }, this);\n  }\n\n  // Redirect to login if not authenticated\n  if (!isAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/login\",\n      state: {\n        from: location\n      },\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 12\n    }, this);\n  }\n\n  // Check admin access if required\n  if (adminOnly && (user === null || user === void 0 ? void 0 : user.role) !== 'admin') {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/dashboard\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: children\n  }, void 0, false);\n};\n_s(ProtectedRoute, \"KoDK5Cisc5kgc5Ecrl3Xz4P5go0=\", false, function () {\n  return [useAuth, useLocation];\n});\n_c = ProtectedRoute;\nexport default ProtectedRoute;\nvar _c;\n$RefreshReg$(_c, \"ProtectedRoute\");", "map": {"version": 3, "names": ["React", "Navigate", "useLocation", "useAuth", "Loading", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProtectedRoute", "children", "adminOnly", "_s", "isAuthenticated", "user", "isLoading", "location", "fullScreen", "text", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "state", "from", "replace", "role", "_c", "$RefreshReg$"], "sources": ["D:/D Drive/Projects/Blogging/blogging-platform/frontend/src/components/ProtectedRoute.tsx"], "sourcesContent": ["import React from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport Loading from './Loading';\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode;\n  adminOnly?: boolean;\n}\n\nconst ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children, adminOnly = false }) => {\n  const { isAuthenticated, user, isLoading } = useAuth();\n  const location = useLocation();\n\n  // Show loading while checking authentication\n  if (isLoading) {\n    return <Loading fullScreen text=\"Checking authentication...\" />;\n  }\n\n  // Redirect to login if not authenticated\n  if (!isAuthenticated) {\n    return <Navigate to=\"/login\" state={{ from: location }} replace />;\n  }\n\n  // Check admin access if required\n  if (adminOnly && user?.role !== 'admin') {\n    return <Navigate to=\"/dashboard\" replace />;\n  }\n\n  return <>{children}</>;\n};\n\nexport default ProtectedRoute;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,EAAEC,WAAW,QAAQ,kBAAkB;AACxD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,OAAO,MAAM,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAOhC,MAAMC,cAA6C,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,SAAS,GAAG;AAAM,CAAC,KAAK;EAAAC,EAAA;EACzF,MAAM;IAAEC,eAAe;IAAEC,IAAI;IAAEC;EAAU,CAAC,GAAGZ,OAAO,CAAC,CAAC;EACtD,MAAMa,QAAQ,GAAGd,WAAW,CAAC,CAAC;;EAE9B;EACA,IAAIa,SAAS,EAAE;IACb,oBAAOT,OAAA,CAACF,OAAO;MAACa,UAAU;MAACC,IAAI,EAAC;IAA4B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACjE;;EAEA;EACA,IAAI,CAACT,eAAe,EAAE;IACpB,oBAAOP,OAAA,CAACL,QAAQ;MAACsB,EAAE,EAAC,QAAQ;MAACC,KAAK,EAAE;QAAEC,IAAI,EAAET;MAAS,CAAE;MAACU,OAAO;IAAA;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACpE;;EAEA;EACA,IAAIX,SAAS,IAAI,CAAAG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEa,IAAI,MAAK,OAAO,EAAE;IACvC,oBAAOrB,OAAA,CAACL,QAAQ;MAACsB,EAAE,EAAC,YAAY;MAACG,OAAO;IAAA;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC7C;EAEA,oBAAOhB,OAAA,CAAAE,SAAA;IAAAE,QAAA,EAAGA;EAAQ,gBAAG,CAAC;AACxB,CAAC;AAACE,EAAA,CApBIH,cAA6C;EAAA,QACJN,OAAO,EACnCD,WAAW;AAAA;AAAA0B,EAAA,GAFxBnB,cAA6C;AAsBnD,eAAeA,cAAc;AAAC,IAAAmB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}