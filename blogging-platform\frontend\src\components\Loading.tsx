import React from 'react';

interface LoadingProps {
  size?: 'sm' | 'md' | 'lg';
  text?: string;
  fullScreen?: boolean;
  className?: string;
}

const Loading: React.FC<LoadingProps> = ({ 
  size = 'md', 
  text, 
  fullScreen = false, 
  className = '' 
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12',
  };

  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
  };

  const spinner = (
    <div className={`animate-spin rounded-full border-2 border-secondary-300 border-t-primary-600 ${sizeClasses[size]}`} />
  );

  const content = (
    <div className={`flex flex-col items-center justify-center space-y-3 ${className}`}>
      {spinner}
      {text && (
        <p className={`text-secondary-600 ${textSizeClasses[size]}`}>
          {text}
        </p>
      )}
    </div>
  );

  if (fullScreen) {
    return (
      <div className="fixed inset-0 bg-white bg-opacity-75 flex items-center justify-center z-50">
        {content}
      </div>
    );
  }

  return content;
};

// Skeleton loading components
export const BlogCardSkeleton: React.FC = () => (
  <div className="card p-6 animate-pulse">
    <div className="flex items-center space-x-3 mb-4">
      <div className="w-10 h-10 bg-secondary-200 rounded-full"></div>
      <div className="space-y-2">
        <div className="h-4 bg-secondary-200 rounded w-24"></div>
        <div className="h-3 bg-secondary-200 rounded w-16"></div>
      </div>
    </div>
    <div className="space-y-3">
      <div className="h-6 bg-secondary-200 rounded w-3/4"></div>
      <div className="h-4 bg-secondary-200 rounded w-full"></div>
      <div className="h-4 bg-secondary-200 rounded w-5/6"></div>
      <div className="h-4 bg-secondary-200 rounded w-4/6"></div>
    </div>
    <div className="flex items-center justify-between mt-6">
      <div className="flex space-x-4">
        <div className="h-4 bg-secondary-200 rounded w-16"></div>
        <div className="h-4 bg-secondary-200 rounded w-16"></div>
      </div>
      <div className="h-6 bg-secondary-200 rounded w-20"></div>
    </div>
  </div>
);

export const CommentSkeleton: React.FC = () => (
  <div className="border-b border-secondary-200 pb-4 animate-pulse">
    <div className="flex space-x-3">
      <div className="w-8 h-8 bg-secondary-200 rounded-full"></div>
      <div className="flex-1 space-y-2">
        <div className="flex items-center space-x-2">
          <div className="h-4 bg-secondary-200 rounded w-20"></div>
          <div className="h-3 bg-secondary-200 rounded w-16"></div>
        </div>
        <div className="space-y-2">
          <div className="h-4 bg-secondary-200 rounded w-full"></div>
          <div className="h-4 bg-secondary-200 rounded w-3/4"></div>
        </div>
        <div className="flex space-x-4">
          <div className="h-3 bg-secondary-200 rounded w-12"></div>
          <div className="h-3 bg-secondary-200 rounded w-12"></div>
        </div>
      </div>
    </div>
  </div>
);

export const BlogDetailSkeleton: React.FC = () => (
  <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8 animate-pulse">
    {/* Header */}
    <div className="space-y-4 mb-8">
      <div className="h-8 bg-secondary-200 rounded w-3/4"></div>
      <div className="flex items-center space-x-4">
        <div className="w-12 h-12 bg-secondary-200 rounded-full"></div>
        <div className="space-y-2">
          <div className="h-4 bg-secondary-200 rounded w-32"></div>
          <div className="h-3 bg-secondary-200 rounded w-24"></div>
        </div>
      </div>
      <div className="flex space-x-4">
        <div className="h-6 bg-secondary-200 rounded w-20"></div>
        <div className="h-6 bg-secondary-200 rounded w-16"></div>
        <div className="h-6 bg-secondary-200 rounded w-24"></div>
      </div>
    </div>

    {/* Featured Image */}
    <div className="h-64 bg-secondary-200 rounded-lg mb-8"></div>

    {/* Content */}
    <div className="space-y-4">
      {Array.from({ length: 8 }).map((_, i) => (
        <div key={i} className="space-y-2">
          <div className="h-4 bg-secondary-200 rounded w-full"></div>
          <div className="h-4 bg-secondary-200 rounded w-5/6"></div>
          {i % 3 === 0 && <div className="h-4 bg-secondary-200 rounded w-4/6"></div>}
        </div>
      ))}
    </div>
  </div>
);

export const UserProfileSkeleton: React.FC = () => (
  <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8 animate-pulse">
    <div className="card p-8">
      <div className="flex items-center space-x-6 mb-8">
        <div className="w-24 h-24 bg-secondary-200 rounded-full"></div>
        <div className="space-y-3">
          <div className="h-6 bg-secondary-200 rounded w-48"></div>
          <div className="h-4 bg-secondary-200 rounded w-32"></div>
          <div className="h-4 bg-secondary-200 rounded w-40"></div>
        </div>
      </div>
      
      <div className="space-y-4">
        <div className="h-4 bg-secondary-200 rounded w-full"></div>
        <div className="h-4 bg-secondary-200 rounded w-3/4"></div>
        <div className="h-4 bg-secondary-200 rounded w-5/6"></div>
      </div>

      <div className="grid grid-cols-3 gap-4 mt-8">
        {Array.from({ length: 3 }).map((_, i) => (
          <div key={i} className="text-center space-y-2">
            <div className="h-8 bg-secondary-200 rounded w-16 mx-auto"></div>
            <div className="h-4 bg-secondary-200 rounded w-20 mx-auto"></div>
          </div>
        ))}
      </div>
    </div>
  </div>
);

export default Loading;
