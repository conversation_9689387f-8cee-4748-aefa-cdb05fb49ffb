{"ast": null, "code": "var _jsxFileName = \"D:\\\\D Drive\\\\Projects\\\\Blogging\\\\blogging-platform\\\\frontend\\\\src\\\\context\\\\AuthContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useReducer, useEffect } from 'react';\nimport { apiService } from '../services/api';\n\n// Auth state type\n\n// Auth actions\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// Initial state\nconst initialState = {\n  user: null,\n  token: null,\n  isLoading: false,\n  error: null\n};\n\n// Auth reducer\nconst authReducer = (state, action) => {\n  switch (action.type) {\n    case 'AUTH_START':\n      return {\n        ...state,\n        isLoading: true,\n        error: null\n      };\n    case 'AUTH_SUCCESS':\n      return {\n        ...state,\n        isLoading: false,\n        user: action.payload.user,\n        token: action.payload.token,\n        error: null\n      };\n    case 'AUTH_FAILURE':\n      return {\n        ...state,\n        isLoading: false,\n        user: null,\n        token: null,\n        error: action.payload\n      };\n    case 'LOGOUT':\n      return {\n        ...state,\n        user: null,\n        token: null,\n        error: null\n      };\n    case 'UPDATE_USER':\n      return {\n        ...state,\n        user: action.payload\n      };\n    case 'CLEAR_ERROR':\n      return {\n        ...state,\n        error: null\n      };\n    default:\n      return state;\n  }\n};\n\n// Create context\nconst AuthContext = /*#__PURE__*/createContext(undefined);\n\n// Auth provider component\n\nexport const AuthProvider = ({\n  children\n}) => {\n  _s();\n  const [state, dispatch] = useReducer(authReducer, initialState);\n\n  // Load user from localStorage on mount\n  useEffect(() => {\n    const loadUser = async () => {\n      const token = localStorage.getItem('token');\n      const userStr = localStorage.getItem('user');\n      if (token && userStr) {\n        try {\n          const user = JSON.parse(userStr);\n          dispatch({\n            type: 'AUTH_SUCCESS',\n            payload: {\n              user,\n              token\n            }\n          });\n\n          // Verify token is still valid by fetching fresh user data\n          const response = await apiService.getProfile();\n          dispatch({\n            type: 'UPDATE_USER',\n            payload: response.user\n          });\n        } catch (error) {\n          // Token is invalid, clear storage\n          localStorage.removeItem('token');\n          localStorage.removeItem('user');\n          dispatch({\n            type: 'LOGOUT'\n          });\n        }\n      }\n    };\n    loadUser();\n  }, []);\n\n  // Login function\n  const login = async credentials => {\n    try {\n      dispatch({\n        type: 'AUTH_START'\n      });\n      const response = await apiService.login(credentials);\n\n      // Store in localStorage\n      localStorage.setItem('token', response.token);\n      localStorage.setItem('user', JSON.stringify(response.user));\n      dispatch({\n        type: 'AUTH_SUCCESS',\n        payload: {\n          user: response.user,\n          token: response.token\n        }\n      });\n    } catch (error) {\n      const apiError = error;\n      dispatch({\n        type: 'AUTH_FAILURE',\n        payload: apiError.message\n      });\n      throw error;\n    }\n  };\n\n  // Register function\n  const register = async data => {\n    try {\n      dispatch({\n        type: 'AUTH_START'\n      });\n      const response = await apiService.register(data);\n\n      // Store in localStorage\n      localStorage.setItem('token', response.token);\n      localStorage.setItem('user', JSON.stringify(response.user));\n      dispatch({\n        type: 'AUTH_SUCCESS',\n        payload: {\n          user: response.user,\n          token: response.token\n        }\n      });\n    } catch (error) {\n      const apiError = error;\n      dispatch({\n        type: 'AUTH_FAILURE',\n        payload: apiError.message\n      });\n      throw error;\n    }\n  };\n\n  // Logout function\n  const logout = () => {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    dispatch({\n      type: 'LOGOUT'\n    });\n  };\n\n  // Update profile function\n  const updateProfile = async data => {\n    try {\n      const response = await apiService.updateProfile(data);\n\n      // Update localStorage\n      localStorage.setItem('user', JSON.stringify(response.user));\n      dispatch({\n        type: 'UPDATE_USER',\n        payload: response.user\n      });\n    } catch (error) {\n      const apiError = error;\n      throw error;\n    }\n  };\n\n  // Change password function\n  const changePassword = async data => {\n    try {\n      await apiService.changePassword(data);\n    } catch (error) {\n      const apiError = error;\n      throw error;\n    }\n  };\n\n  // Clear error function\n  const clearError = () => {\n    dispatch({\n      type: 'CLEAR_ERROR'\n    });\n  };\n\n  // Context value\n  const value = {\n    user: state.user,\n    token: state.token,\n    isLoading: state.isLoading,\n    isAuthenticated: !!state.user && !!state.token,\n    login,\n    register,\n    logout,\n    updateProfile,\n    changePassword\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 209,\n    columnNumber: 5\n  }, this);\n};\n\n// Custom hook to use auth context\n_s(AuthProvider, \"bgCdjuTOmPdSBRwTap80EFd9Y3U=\");\n_c = AuthProvider;\nexport const useAuth = () => {\n  _s2();\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s2(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport default AuthContext;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useReducer", "useEffect", "apiService", "jsxDEV", "_jsxDEV", "initialState", "user", "token", "isLoading", "error", "authReducer", "state", "action", "type", "payload", "AuthContext", "undefined", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s", "dispatch", "loadUser", "localStorage", "getItem", "userStr", "JSON", "parse", "response", "getProfile", "removeItem", "login", "credentials", "setItem", "stringify", "apiError", "message", "register", "data", "logout", "updateProfile", "changePassword", "clearError", "value", "isAuthenticated", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useAuth", "_s2", "context", "Error", "$RefreshReg$"], "sources": ["D:/D Drive/Projects/Blogging/blogging-platform/frontend/src/context/AuthContext.tsx"], "sourcesContent": ["import React, { create<PERSON>ontext, useContext, useReducer, useEffect, ReactNode } from 'react';\nimport { User, LoginCredentials, RegisterData, ProfileUpdateData, PasswordChangeData, AuthContextType, ApiError } from '../types';\nimport { apiService } from '../services/api';\n\n// Auth state type\ninterface AuthState {\n  user: User | null;\n  token: string | null;\n  isLoading: boolean;\n  error: string | null;\n}\n\n// Auth actions\ntype AuthAction =\n  | { type: 'AUTH_START' }\n  | { type: 'AUTH_SUCCESS'; payload: { user: User; token: string } }\n  | { type: 'AUTH_FAILURE'; payload: string }\n  | { type: 'LOGOUT' }\n  | { type: 'UPDATE_USER'; payload: User }\n  | { type: 'CLEAR_ERROR' };\n\n// Initial state\nconst initialState: AuthState = {\n  user: null,\n  token: null,\n  isLoading: false,\n  error: null,\n};\n\n// Auth reducer\nconst authReducer = (state: AuthState, action: AuthAction): AuthState => {\n  switch (action.type) {\n    case 'AUTH_START':\n      return {\n        ...state,\n        isLoading: true,\n        error: null,\n      };\n    case 'AUTH_SUCCESS':\n      return {\n        ...state,\n        isLoading: false,\n        user: action.payload.user,\n        token: action.payload.token,\n        error: null,\n      };\n    case 'AUTH_FAILURE':\n      return {\n        ...state,\n        isLoading: false,\n        user: null,\n        token: null,\n        error: action.payload,\n      };\n    case 'LOGOUT':\n      return {\n        ...state,\n        user: null,\n        token: null,\n        error: null,\n      };\n    case 'UPDATE_USER':\n      return {\n        ...state,\n        user: action.payload,\n      };\n    case 'CLEAR_ERROR':\n      return {\n        ...state,\n        error: null,\n      };\n    default:\n      return state;\n  }\n};\n\n// Create context\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\n// Auth provider component\ninterface AuthProviderProps {\n  children: ReactNode;\n}\n\nexport const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {\n  const [state, dispatch] = useReducer(authReducer, initialState);\n\n  // Load user from localStorage on mount\n  useEffect(() => {\n    const loadUser = async () => {\n      const token = localStorage.getItem('token');\n      const userStr = localStorage.getItem('user');\n\n      if (token && userStr) {\n        try {\n          const user = JSON.parse(userStr);\n          dispatch({ type: 'AUTH_SUCCESS', payload: { user, token } });\n          \n          // Verify token is still valid by fetching fresh user data\n          const response = await apiService.getProfile();\n          dispatch({ type: 'UPDATE_USER', payload: response.user });\n        } catch (error) {\n          // Token is invalid, clear storage\n          localStorage.removeItem('token');\n          localStorage.removeItem('user');\n          dispatch({ type: 'LOGOUT' });\n        }\n      }\n    };\n\n    loadUser();\n  }, []);\n\n  // Login function\n  const login = async (credentials: LoginCredentials): Promise<void> => {\n    try {\n      dispatch({ type: 'AUTH_START' });\n      \n      const response = await apiService.login(credentials);\n      \n      // Store in localStorage\n      localStorage.setItem('token', response.token);\n      localStorage.setItem('user', JSON.stringify(response.user));\n      \n      dispatch({ \n        type: 'AUTH_SUCCESS', \n        payload: { user: response.user, token: response.token } \n      });\n    } catch (error) {\n      const apiError = error as ApiError;\n      dispatch({ type: 'AUTH_FAILURE', payload: apiError.message });\n      throw error;\n    }\n  };\n\n  // Register function\n  const register = async (data: RegisterData): Promise<void> => {\n    try {\n      dispatch({ type: 'AUTH_START' });\n      \n      const response = await apiService.register(data);\n      \n      // Store in localStorage\n      localStorage.setItem('token', response.token);\n      localStorage.setItem('user', JSON.stringify(response.user));\n      \n      dispatch({ \n        type: 'AUTH_SUCCESS', \n        payload: { user: response.user, token: response.token } \n      });\n    } catch (error) {\n      const apiError = error as ApiError;\n      dispatch({ type: 'AUTH_FAILURE', payload: apiError.message });\n      throw error;\n    }\n  };\n\n  // Logout function\n  const logout = (): void => {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    dispatch({ type: 'LOGOUT' });\n  };\n\n  // Update profile function\n  const updateProfile = async (data: ProfileUpdateData): Promise<void> => {\n    try {\n      const response = await apiService.updateProfile(data);\n      \n      // Update localStorage\n      localStorage.setItem('user', JSON.stringify(response.user));\n      \n      dispatch({ type: 'UPDATE_USER', payload: response.user });\n    } catch (error) {\n      const apiError = error as ApiError;\n      throw error;\n    }\n  };\n\n  // Change password function\n  const changePassword = async (data: PasswordChangeData): Promise<void> => {\n    try {\n      await apiService.changePassword(data);\n    } catch (error) {\n      const apiError = error as ApiError;\n      throw error;\n    }\n  };\n\n  // Clear error function\n  const clearError = (): void => {\n    dispatch({ type: 'CLEAR_ERROR' });\n  };\n\n  // Context value\n  const value: AuthContextType = {\n    user: state.user,\n    token: state.token,\n    isLoading: state.isLoading,\n    isAuthenticated: !!state.user && !!state.token,\n    login,\n    register,\n    logout,\n    updateProfile,\n    changePassword,\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\n// Custom hook to use auth context\nexport const useAuth = (): AuthContextType => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport default AuthContext;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,UAAU,EAAEC,SAAS,QAAmB,OAAO;AAE1F,SAASC,UAAU,QAAQ,iBAAiB;;AAE5C;;AAQA;AAAA,SAAAC,MAAA,IAAAC,OAAA;AASA;AACA,MAAMC,YAAuB,GAAG;EAC9BC,IAAI,EAAE,IAAI;EACVC,KAAK,EAAE,IAAI;EACXC,SAAS,EAAE,KAAK;EAChBC,KAAK,EAAE;AACT,CAAC;;AAED;AACA,MAAMC,WAAW,GAAGA,CAACC,KAAgB,EAAEC,MAAkB,KAAgB;EACvE,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAK,YAAY;MACf,OAAO;QACL,GAAGF,KAAK;QACRH,SAAS,EAAE,IAAI;QACfC,KAAK,EAAE;MACT,CAAC;IACH,KAAK,cAAc;MACjB,OAAO;QACL,GAAGE,KAAK;QACRH,SAAS,EAAE,KAAK;QAChBF,IAAI,EAAEM,MAAM,CAACE,OAAO,CAACR,IAAI;QACzBC,KAAK,EAAEK,MAAM,CAACE,OAAO,CAACP,KAAK;QAC3BE,KAAK,EAAE;MACT,CAAC;IACH,KAAK,cAAc;MACjB,OAAO;QACL,GAAGE,KAAK;QACRH,SAAS,EAAE,KAAK;QAChBF,IAAI,EAAE,IAAI;QACVC,KAAK,EAAE,IAAI;QACXE,KAAK,EAAEG,MAAM,CAACE;MAChB,CAAC;IACH,KAAK,QAAQ;MACX,OAAO;QACL,GAAGH,KAAK;QACRL,IAAI,EAAE,IAAI;QACVC,KAAK,EAAE,IAAI;QACXE,KAAK,EAAE;MACT,CAAC;IACH,KAAK,aAAa;MAChB,OAAO;QACL,GAAGE,KAAK;QACRL,IAAI,EAAEM,MAAM,CAACE;MACf,CAAC;IACH,KAAK,aAAa;MAChB,OAAO;QACL,GAAGH,KAAK;QACRF,KAAK,EAAE;MACT,CAAC;IACH;MACE,OAAOE,KAAK;EAChB;AACF,CAAC;;AAED;AACA,MAAMI,WAAW,gBAAGjB,aAAa,CAA8BkB,SAAS,CAAC;;AAEzE;;AAKA,OAAO,MAAMC,YAAyC,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACzE,MAAM,CAACR,KAAK,EAAES,QAAQ,CAAC,GAAGpB,UAAU,CAACU,WAAW,EAAEL,YAAY,CAAC;;EAE/D;EACAJ,SAAS,CAAC,MAAM;IACd,MAAMoB,QAAQ,GAAG,MAAAA,CAAA,KAAY;MAC3B,MAAMd,KAAK,GAAGe,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMC,OAAO,GAAGF,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAE5C,IAAIhB,KAAK,IAAIiB,OAAO,EAAE;QACpB,IAAI;UACF,MAAMlB,IAAI,GAAGmB,IAAI,CAACC,KAAK,CAACF,OAAO,CAAC;UAChCJ,QAAQ,CAAC;YAAEP,IAAI,EAAE,cAAc;YAAEC,OAAO,EAAE;cAAER,IAAI;cAAEC;YAAM;UAAE,CAAC,CAAC;;UAE5D;UACA,MAAMoB,QAAQ,GAAG,MAAMzB,UAAU,CAAC0B,UAAU,CAAC,CAAC;UAC9CR,QAAQ,CAAC;YAAEP,IAAI,EAAE,aAAa;YAAEC,OAAO,EAAEa,QAAQ,CAACrB;UAAK,CAAC,CAAC;QAC3D,CAAC,CAAC,OAAOG,KAAK,EAAE;UACd;UACAa,YAAY,CAACO,UAAU,CAAC,OAAO,CAAC;UAChCP,YAAY,CAACO,UAAU,CAAC,MAAM,CAAC;UAC/BT,QAAQ,CAAC;YAAEP,IAAI,EAAE;UAAS,CAAC,CAAC;QAC9B;MACF;IACF,CAAC;IAEDQ,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMS,KAAK,GAAG,MAAOC,WAA6B,IAAoB;IACpE,IAAI;MACFX,QAAQ,CAAC;QAAEP,IAAI,EAAE;MAAa,CAAC,CAAC;MAEhC,MAAMc,QAAQ,GAAG,MAAMzB,UAAU,CAAC4B,KAAK,CAACC,WAAW,CAAC;;MAEpD;MACAT,YAAY,CAACU,OAAO,CAAC,OAAO,EAAEL,QAAQ,CAACpB,KAAK,CAAC;MAC7Ce,YAAY,CAACU,OAAO,CAAC,MAAM,EAAEP,IAAI,CAACQ,SAAS,CAACN,QAAQ,CAACrB,IAAI,CAAC,CAAC;MAE3Dc,QAAQ,CAAC;QACPP,IAAI,EAAE,cAAc;QACpBC,OAAO,EAAE;UAAER,IAAI,EAAEqB,QAAQ,CAACrB,IAAI;UAAEC,KAAK,EAAEoB,QAAQ,CAACpB;QAAM;MACxD,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOE,KAAK,EAAE;MACd,MAAMyB,QAAQ,GAAGzB,KAAiB;MAClCW,QAAQ,CAAC;QAAEP,IAAI,EAAE,cAAc;QAAEC,OAAO,EAAEoB,QAAQ,CAACC;MAAQ,CAAC,CAAC;MAC7D,MAAM1B,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAM2B,QAAQ,GAAG,MAAOC,IAAkB,IAAoB;IAC5D,IAAI;MACFjB,QAAQ,CAAC;QAAEP,IAAI,EAAE;MAAa,CAAC,CAAC;MAEhC,MAAMc,QAAQ,GAAG,MAAMzB,UAAU,CAACkC,QAAQ,CAACC,IAAI,CAAC;;MAEhD;MACAf,YAAY,CAACU,OAAO,CAAC,OAAO,EAAEL,QAAQ,CAACpB,KAAK,CAAC;MAC7Ce,YAAY,CAACU,OAAO,CAAC,MAAM,EAAEP,IAAI,CAACQ,SAAS,CAACN,QAAQ,CAACrB,IAAI,CAAC,CAAC;MAE3Dc,QAAQ,CAAC;QACPP,IAAI,EAAE,cAAc;QACpBC,OAAO,EAAE;UAAER,IAAI,EAAEqB,QAAQ,CAACrB,IAAI;UAAEC,KAAK,EAAEoB,QAAQ,CAACpB;QAAM;MACxD,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOE,KAAK,EAAE;MACd,MAAMyB,QAAQ,GAAGzB,KAAiB;MAClCW,QAAQ,CAAC;QAAEP,IAAI,EAAE,cAAc;QAAEC,OAAO,EAAEoB,QAAQ,CAACC;MAAQ,CAAC,CAAC;MAC7D,MAAM1B,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAM6B,MAAM,GAAGA,CAAA,KAAY;IACzBhB,YAAY,CAACO,UAAU,CAAC,OAAO,CAAC;IAChCP,YAAY,CAACO,UAAU,CAAC,MAAM,CAAC;IAC/BT,QAAQ,CAAC;MAAEP,IAAI,EAAE;IAAS,CAAC,CAAC;EAC9B,CAAC;;EAED;EACA,MAAM0B,aAAa,GAAG,MAAOF,IAAuB,IAAoB;IACtE,IAAI;MACF,MAAMV,QAAQ,GAAG,MAAMzB,UAAU,CAACqC,aAAa,CAACF,IAAI,CAAC;;MAErD;MACAf,YAAY,CAACU,OAAO,CAAC,MAAM,EAAEP,IAAI,CAACQ,SAAS,CAACN,QAAQ,CAACrB,IAAI,CAAC,CAAC;MAE3Dc,QAAQ,CAAC;QAAEP,IAAI,EAAE,aAAa;QAAEC,OAAO,EAAEa,QAAQ,CAACrB;MAAK,CAAC,CAAC;IAC3D,CAAC,CAAC,OAAOG,KAAK,EAAE;MACd,MAAMyB,QAAQ,GAAGzB,KAAiB;MAClC,MAAMA,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAM+B,cAAc,GAAG,MAAOH,IAAwB,IAAoB;IACxE,IAAI;MACF,MAAMnC,UAAU,CAACsC,cAAc,CAACH,IAAI,CAAC;IACvC,CAAC,CAAC,OAAO5B,KAAK,EAAE;MACd,MAAMyB,QAAQ,GAAGzB,KAAiB;MAClC,MAAMA,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAMgC,UAAU,GAAGA,CAAA,KAAY;IAC7BrB,QAAQ,CAAC;MAAEP,IAAI,EAAE;IAAc,CAAC,CAAC;EACnC,CAAC;;EAED;EACA,MAAM6B,KAAsB,GAAG;IAC7BpC,IAAI,EAAEK,KAAK,CAACL,IAAI;IAChBC,KAAK,EAAEI,KAAK,CAACJ,KAAK;IAClBC,SAAS,EAAEG,KAAK,CAACH,SAAS;IAC1BmC,eAAe,EAAE,CAAC,CAAChC,KAAK,CAACL,IAAI,IAAI,CAAC,CAACK,KAAK,CAACJ,KAAK;IAC9CuB,KAAK;IACLM,QAAQ;IACRE,MAAM;IACNC,aAAa;IACbC;EACF,CAAC;EAED,oBACEpC,OAAA,CAACW,WAAW,CAAC6B,QAAQ;IAACF,KAAK,EAAEA,KAAM;IAAAxB,QAAA,EAChCA;EAAQ;IAAA2B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;;AAED;AAAA7B,EAAA,CAlIaF,YAAyC;AAAAgC,EAAA,GAAzChC,YAAyC;AAmItD,OAAO,MAAMiC,OAAO,GAAGA,CAAA,KAAuB;EAAAC,GAAA;EAC5C,MAAMC,OAAO,GAAGrD,UAAU,CAACgB,WAAW,CAAC;EACvC,IAAIqC,OAAO,KAAKpC,SAAS,EAAE;IACzB,MAAM,IAAIqC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,OAAO;AAQpB,eAAenC,WAAW;AAAC,IAAAkC,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}