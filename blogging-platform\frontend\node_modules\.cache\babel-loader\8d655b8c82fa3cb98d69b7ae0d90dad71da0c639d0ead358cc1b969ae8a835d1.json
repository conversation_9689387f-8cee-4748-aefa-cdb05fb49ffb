{"ast": null, "code": "var _jsxFileName = \"D:\\\\D Drive\\\\Projects\\\\Blogging\\\\blogging-platform\\\\frontend\\\\src\\\\pages\\\\NotFound.tsx\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { Helmet } from 'react-helmet-async';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst NotFound = () => {\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Helmet, {\n      children: [/*#__PURE__*/_jsxDEV(\"title\", {\n        children: \"Page Not Found - BlogPlatform\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        name: \"description\",\n        content: \"The page you are looking for does not exist.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-8\",\n          children: /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-9xl font-bold text-primary-600\",\n            children: \"404\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 16,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-3xl md:text-4xl font-bold text-secondary-900 mb-4\",\n          children: \"Page Not Found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-secondary-600 mb-8 max-w-md mx-auto\",\n          children: \"Sorry, we couldn't find the page you're looking for. It might have been moved, deleted, or you entered the wrong URL.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            className: \"btn-primary\",\n            children: \"Go Home\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/blogs\",\n            className: \"btn-outline\",\n            children: \"Browse Blogs\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 31,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-12\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-secondary-500\",\n            children: [\"If you believe this is an error, please\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/contact\",\n              className: \"text-primary-600 hover:text-primary-500\",\n              children: \"contact us\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 39,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_c = NotFound;\nexport default NotFound;\nvar _c;\n$RefreshReg$(_c, \"NotFound\");", "map": {"version": 3, "names": ["React", "Link", "<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "NotFound", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "content", "className", "to", "_c", "$RefreshReg$"], "sources": ["D:/D Drive/Projects/Blogging/blogging-platform/frontend/src/pages/NotFound.tsx"], "sourcesContent": ["import React from 'react';\nimport { <PERSON> } from 'react-router-dom';\nimport { Helmet } from 'react-helmet-async';\n\nconst NotFound: React.FC = () => {\n  return (\n    <>\n      <Helmet>\n        <title>Page Not Found - BlogPlatform</title>\n        <meta name=\"description\" content=\"The page you are looking for does not exist.\" />\n      </Helmet>\n\n      <div className=\"min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center\">\n          <div className=\"mb-8\">\n            <h1 className=\"text-9xl font-bold text-primary-600\">404</h1>\n          </div>\n          \n          <h2 className=\"text-3xl md:text-4xl font-bold text-secondary-900 mb-4\">\n            Page Not Found\n          </h2>\n          \n          <p className=\"text-lg text-secondary-600 mb-8 max-w-md mx-auto\">\n            Sorry, we couldn't find the page you're looking for. It might have been moved, deleted, or you entered the wrong URL.\n          </p>\n          \n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Link to=\"/\" className=\"btn-primary\">\n              Go Home\n            </Link>\n            <Link to=\"/blogs\" className=\"btn-outline\">\n              Browse Blogs\n            </Link>\n          </div>\n          \n          <div className=\"mt-12\">\n            <p className=\"text-sm text-secondary-500\">\n              If you believe this is an error, please{' '}\n              <Link to=\"/contact\" className=\"text-primary-600 hover:text-primary-500\">\n                contact us\n              </Link>\n            </p>\n          </div>\n        </div>\n      </div>\n    </>\n  );\n};\n\nexport default NotFound;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,MAAM,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE5C,MAAMC,QAAkB,GAAGA,CAAA,KAAM;EAC/B,oBACEH,OAAA,CAAAE,SAAA;IAAAE,QAAA,gBACEJ,OAAA,CAACF,MAAM;MAAAM,QAAA,gBACLJ,OAAA;QAAAI,QAAA,EAAO;MAA6B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC5CR,OAAA;QAAMS,IAAI,EAAC,aAAa;QAACC,OAAO,EAAC;MAA8C;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5E,CAAC,eAETR,OAAA;MAAKW,SAAS,EAAC,oEAAoE;MAAAP,QAAA,eACjFJ,OAAA;QAAKW,SAAS,EAAC,aAAa;QAAAP,QAAA,gBAC1BJ,OAAA;UAAKW,SAAS,EAAC,MAAM;UAAAP,QAAA,eACnBJ,OAAA;YAAIW,SAAS,EAAC,qCAAqC;YAAAP,QAAA,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC,eAENR,OAAA;UAAIW,SAAS,EAAC,wDAAwD;UAAAP,QAAA,EAAC;QAEvE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAELR,OAAA;UAAGW,SAAS,EAAC,kDAAkD;UAAAP,QAAA,EAAC;QAEhE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAEJR,OAAA;UAAKW,SAAS,EAAC,gDAAgD;UAAAP,QAAA,gBAC7DJ,OAAA,CAACH,IAAI;YAACe,EAAE,EAAC,GAAG;YAACD,SAAS,EAAC,aAAa;YAAAP,QAAA,EAAC;UAErC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPR,OAAA,CAACH,IAAI;YAACe,EAAE,EAAC,QAAQ;YAACD,SAAS,EAAC,aAAa;YAAAP,QAAA,EAAC;UAE1C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAENR,OAAA;UAAKW,SAAS,EAAC,OAAO;UAAAP,QAAA,eACpBJ,OAAA;YAAGW,SAAS,EAAC,4BAA4B;YAAAP,QAAA,GAAC,yCACD,EAAC,GAAG,eAC3CJ,OAAA,CAACH,IAAI;cAACe,EAAE,EAAC,UAAU;cAACD,SAAS,EAAC,yCAAyC;cAAAP,QAAA,EAAC;YAExE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAACK,EAAA,GA3CIV,QAAkB;AA6CxB,eAAeA,QAAQ;AAAC,IAAAU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}