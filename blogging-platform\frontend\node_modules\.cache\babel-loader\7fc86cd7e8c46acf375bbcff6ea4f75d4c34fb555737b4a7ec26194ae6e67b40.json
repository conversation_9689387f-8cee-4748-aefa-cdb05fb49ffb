{"ast": null, "code": "var _jsxFileName = \"D:\\\\D Drive\\\\Projects\\\\Blogging\\\\blogging-platform\\\\frontend\\\\src\\\\components\\\\Loading.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Loading = ({\n  size = 'md',\n  text,\n  fullScreen = false,\n  className = ''\n}) => {\n  const sizeClasses = {\n    sm: 'w-4 h-4',\n    md: 'w-8 h-8',\n    lg: 'w-12 h-12'\n  };\n  const textSizeClasses = {\n    sm: 'text-sm',\n    md: 'text-base',\n    lg: 'text-lg'\n  };\n  const spinner = /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `animate-spin rounded-full border-2 border-secondary-300 border-t-primary-600 ${sizeClasses[size]}`\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 29,\n    columnNumber: 5\n  }, this);\n  const content = /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `flex flex-col items-center justify-center space-y-3 ${className}`,\n    children: [spinner, text && /*#__PURE__*/_jsxDEV(\"p\", {\n      className: `text-secondary-600 ${textSizeClasses[size]}`,\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 33,\n    columnNumber: 5\n  }, this);\n  if (fullScreen) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-white bg-opacity-75 flex items-center justify-center z-50\",\n      children: content\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this);\n  }\n  return content;\n};\n\n// Skeleton loading components\n_c = Loading;\nexport const BlogCardSkeleton = () => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"card p-6 animate-pulse\",\n  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex items-center space-x-3 mb-4\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"w-10 h-10 bg-secondary-200 rounded-full\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"h-4 bg-secondary-200 rounded w-24\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"h-3 bg-secondary-200 rounded w-16\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 57,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-3\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"h-6 bg-secondary-200 rounded w-3/4\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"h-4 bg-secondary-200 rounded w-full\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"h-4 bg-secondary-200 rounded w-5/6\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"h-4 bg-secondary-200 rounded w-4/6\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 64,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex items-center justify-between mt-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex space-x-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"h-4 bg-secondary-200 rounded w-16\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"h-4 bg-secondary-200 rounded w-16\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"h-6 bg-secondary-200 rounded w-20\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 70,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 56,\n  columnNumber: 3\n}, this);\n_c2 = BlogCardSkeleton;\nexport const CommentSkeleton = () => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"border-b border-secondary-200 pb-4 animate-pulse\",\n  children: /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex space-x-3\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"w-8 h-8 bg-secondary-200 rounded-full\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 space-y-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-4 bg-secondary-200 rounded w-20\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-3 bg-secondary-200 rounded w-16\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-4 bg-secondary-200 rounded w-full\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-4 bg-secondary-200 rounded w-3/4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex space-x-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-3 bg-secondary-200 rounded w-12\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-3 bg-secondary-200 rounded w-12\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 82,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 81,\n  columnNumber: 3\n}, this);\n_c3 = CommentSkeleton;\nexport const BlogDetailSkeleton = () => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8 animate-pulse\",\n  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-4 mb-8\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"h-8 bg-secondary-200 rounded w-3/4\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center space-x-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-12 h-12 bg-secondary-200 rounded-full\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-4 bg-secondary-200 rounded w-32\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-3 bg-secondary-200 rounded w-24\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex space-x-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"h-6 bg-secondary-200 rounded w-20\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"h-6 bg-secondary-200 rounded w-16\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"h-6 bg-secondary-200 rounded w-24\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 105,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"h-64 bg-secondary-200 rounded-lg mb-8\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 122,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-4\",\n    children: Array.from({\n      length: 8\n    }).map((_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"h-4 bg-secondary-200 rounded w-full\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"h-4 bg-secondary-200 rounded w-5/6\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 11\n      }, this), i % 3 === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"h-4 bg-secondary-200 rounded w-4/6\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 27\n      }, this)]\n    }, i, true, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 9\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 125,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 103,\n  columnNumber: 3\n}, this);\n_c4 = BlogDetailSkeleton;\nexport const UserProfileSkeleton = () => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8 animate-pulse\",\n  children: /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"card p-8\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center space-x-6 mb-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-24 h-24 bg-secondary-200 rounded-full\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-6 bg-secondary-200 rounded w-48\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-4 bg-secondary-200 rounded w-32\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-4 bg-secondary-200 rounded w-40\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"h-4 bg-secondary-200 rounded w-full\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"h-4 bg-secondary-200 rounded w-3/4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"h-4 bg-secondary-200 rounded w-5/6\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-3 gap-4 mt-8\",\n      children: Array.from({\n        length: 3\n      }).map((_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center space-y-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-8 bg-secondary-200 rounded w-16 mx-auto\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-4 bg-secondary-200 rounded w-20 mx-auto\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 13\n        }, this)]\n      }, i, true, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 139,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 138,\n  columnNumber: 3\n}, this);\n_c5 = UserProfileSkeleton;\nexport default Loading;\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"Loading\");\n$RefreshReg$(_c2, \"BlogCardSkeleton\");\n$RefreshReg$(_c3, \"CommentSkeleton\");\n$RefreshReg$(_c4, \"BlogDetailSkeleton\");\n$RefreshReg$(_c5, \"UserProfileSkeleton\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Loading", "size", "text", "fullScreen", "className", "sizeClasses", "sm", "md", "lg", "textSizeClasses", "spinner", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "content", "children", "_c", "BlogCardSkeleton", "_c2", "CommentSkeleton", "_c3", "BlogDetailSkeleton", "Array", "from", "length", "map", "_", "i", "_c4", "UserProfileSkeleton", "_c5", "$RefreshReg$"], "sources": ["D:/D Drive/Projects/Blogging/blogging-platform/frontend/src/components/Loading.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface LoadingProps {\n  size?: 'sm' | 'md' | 'lg';\n  text?: string;\n  fullScreen?: boolean;\n  className?: string;\n}\n\nconst Loading: React.FC<LoadingProps> = ({ \n  size = 'md', \n  text, \n  fullScreen = false, \n  className = '' \n}) => {\n  const sizeClasses = {\n    sm: 'w-4 h-4',\n    md: 'w-8 h-8',\n    lg: 'w-12 h-12',\n  };\n\n  const textSizeClasses = {\n    sm: 'text-sm',\n    md: 'text-base',\n    lg: 'text-lg',\n  };\n\n  const spinner = (\n    <div className={`animate-spin rounded-full border-2 border-secondary-300 border-t-primary-600 ${sizeClasses[size]}`} />\n  );\n\n  const content = (\n    <div className={`flex flex-col items-center justify-center space-y-3 ${className}`}>\n      {spinner}\n      {text && (\n        <p className={`text-secondary-600 ${textSizeClasses[size]}`}>\n          {text}\n        </p>\n      )}\n    </div>\n  );\n\n  if (fullScreen) {\n    return (\n      <div className=\"fixed inset-0 bg-white bg-opacity-75 flex items-center justify-center z-50\">\n        {content}\n      </div>\n    );\n  }\n\n  return content;\n};\n\n// Skeleton loading components\nexport const BlogCardSkeleton: React.FC = () => (\n  <div className=\"card p-6 animate-pulse\">\n    <div className=\"flex items-center space-x-3 mb-4\">\n      <div className=\"w-10 h-10 bg-secondary-200 rounded-full\"></div>\n      <div className=\"space-y-2\">\n        <div className=\"h-4 bg-secondary-200 rounded w-24\"></div>\n        <div className=\"h-3 bg-secondary-200 rounded w-16\"></div>\n      </div>\n    </div>\n    <div className=\"space-y-3\">\n      <div className=\"h-6 bg-secondary-200 rounded w-3/4\"></div>\n      <div className=\"h-4 bg-secondary-200 rounded w-full\"></div>\n      <div className=\"h-4 bg-secondary-200 rounded w-5/6\"></div>\n      <div className=\"h-4 bg-secondary-200 rounded w-4/6\"></div>\n    </div>\n    <div className=\"flex items-center justify-between mt-6\">\n      <div className=\"flex space-x-4\">\n        <div className=\"h-4 bg-secondary-200 rounded w-16\"></div>\n        <div className=\"h-4 bg-secondary-200 rounded w-16\"></div>\n      </div>\n      <div className=\"h-6 bg-secondary-200 rounded w-20\"></div>\n    </div>\n  </div>\n);\n\nexport const CommentSkeleton: React.FC = () => (\n  <div className=\"border-b border-secondary-200 pb-4 animate-pulse\">\n    <div className=\"flex space-x-3\">\n      <div className=\"w-8 h-8 bg-secondary-200 rounded-full\"></div>\n      <div className=\"flex-1 space-y-2\">\n        <div className=\"flex items-center space-x-2\">\n          <div className=\"h-4 bg-secondary-200 rounded w-20\"></div>\n          <div className=\"h-3 bg-secondary-200 rounded w-16\"></div>\n        </div>\n        <div className=\"space-y-2\">\n          <div className=\"h-4 bg-secondary-200 rounded w-full\"></div>\n          <div className=\"h-4 bg-secondary-200 rounded w-3/4\"></div>\n        </div>\n        <div className=\"flex space-x-4\">\n          <div className=\"h-3 bg-secondary-200 rounded w-12\"></div>\n          <div className=\"h-3 bg-secondary-200 rounded w-12\"></div>\n        </div>\n      </div>\n    </div>\n  </div>\n);\n\nexport const BlogDetailSkeleton: React.FC = () => (\n  <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8 animate-pulse\">\n    {/* Header */}\n    <div className=\"space-y-4 mb-8\">\n      <div className=\"h-8 bg-secondary-200 rounded w-3/4\"></div>\n      <div className=\"flex items-center space-x-4\">\n        <div className=\"w-12 h-12 bg-secondary-200 rounded-full\"></div>\n        <div className=\"space-y-2\">\n          <div className=\"h-4 bg-secondary-200 rounded w-32\"></div>\n          <div className=\"h-3 bg-secondary-200 rounded w-24\"></div>\n        </div>\n      </div>\n      <div className=\"flex space-x-4\">\n        <div className=\"h-6 bg-secondary-200 rounded w-20\"></div>\n        <div className=\"h-6 bg-secondary-200 rounded w-16\"></div>\n        <div className=\"h-6 bg-secondary-200 rounded w-24\"></div>\n      </div>\n    </div>\n\n    {/* Featured Image */}\n    <div className=\"h-64 bg-secondary-200 rounded-lg mb-8\"></div>\n\n    {/* Content */}\n    <div className=\"space-y-4\">\n      {Array.from({ length: 8 }).map((_, i) => (\n        <div key={i} className=\"space-y-2\">\n          <div className=\"h-4 bg-secondary-200 rounded w-full\"></div>\n          <div className=\"h-4 bg-secondary-200 rounded w-5/6\"></div>\n          {i % 3 === 0 && <div className=\"h-4 bg-secondary-200 rounded w-4/6\"></div>}\n        </div>\n      ))}\n    </div>\n  </div>\n);\n\nexport const UserProfileSkeleton: React.FC = () => (\n  <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8 animate-pulse\">\n    <div className=\"card p-8\">\n      <div className=\"flex items-center space-x-6 mb-8\">\n        <div className=\"w-24 h-24 bg-secondary-200 rounded-full\"></div>\n        <div className=\"space-y-3\">\n          <div className=\"h-6 bg-secondary-200 rounded w-48\"></div>\n          <div className=\"h-4 bg-secondary-200 rounded w-32\"></div>\n          <div className=\"h-4 bg-secondary-200 rounded w-40\"></div>\n        </div>\n      </div>\n      \n      <div className=\"space-y-4\">\n        <div className=\"h-4 bg-secondary-200 rounded w-full\"></div>\n        <div className=\"h-4 bg-secondary-200 rounded w-3/4\"></div>\n        <div className=\"h-4 bg-secondary-200 rounded w-5/6\"></div>\n      </div>\n\n      <div className=\"grid grid-cols-3 gap-4 mt-8\">\n        {Array.from({ length: 3 }).map((_, i) => (\n          <div key={i} className=\"text-center space-y-2\">\n            <div className=\"h-8 bg-secondary-200 rounded w-16 mx-auto\"></div>\n            <div className=\"h-4 bg-secondary-200 rounded w-20 mx-auto\"></div>\n          </div>\n        ))}\n      </div>\n    </div>\n  </div>\n);\n\nexport default Loading;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAS1B,MAAMC,OAA+B,GAAGA,CAAC;EACvCC,IAAI,GAAG,IAAI;EACXC,IAAI;EACJC,UAAU,GAAG,KAAK;EAClBC,SAAS,GAAG;AACd,CAAC,KAAK;EACJ,MAAMC,WAAW,GAAG;IAClBC,EAAE,EAAE,SAAS;IACbC,EAAE,EAAE,SAAS;IACbC,EAAE,EAAE;EACN,CAAC;EAED,MAAMC,eAAe,GAAG;IACtBH,EAAE,EAAE,SAAS;IACbC,EAAE,EAAE,WAAW;IACfC,EAAE,EAAE;EACN,CAAC;EAED,MAAME,OAAO,gBACXX,OAAA;IAAKK,SAAS,EAAE,gFAAgFC,WAAW,CAACJ,IAAI,CAAC;EAAG;IAAAU,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CACvH;EAED,MAAMC,OAAO,gBACXhB,OAAA;IAAKK,SAAS,EAAE,uDAAuDA,SAAS,EAAG;IAAAY,QAAA,GAChFN,OAAO,EACPR,IAAI,iBACHH,OAAA;MAAGK,SAAS,EAAE,sBAAsBK,eAAe,CAACR,IAAI,CAAC,EAAG;MAAAe,QAAA,EACzDd;IAAI;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CACJ;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CACN;EAED,IAAIX,UAAU,EAAE;IACd,oBACEJ,OAAA;MAAKK,SAAS,EAAC,4EAA4E;MAAAY,QAAA,EACxFD;IAAO;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV;EAEA,OAAOC,OAAO;AAChB,CAAC;;AAED;AAAAE,EAAA,GA5CMjB,OAA+B;AA6CrC,OAAO,MAAMkB,gBAA0B,GAAGA,CAAA,kBACxCnB,OAAA;EAAKK,SAAS,EAAC,wBAAwB;EAAAY,QAAA,gBACrCjB,OAAA;IAAKK,SAAS,EAAC,kCAAkC;IAAAY,QAAA,gBAC/CjB,OAAA;MAAKK,SAAS,EAAC;IAAyC;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAC/Df,OAAA;MAAKK,SAAS,EAAC,WAAW;MAAAY,QAAA,gBACxBjB,OAAA;QAAKK,SAAS,EAAC;MAAmC;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACzDf,OAAA;QAAKK,SAAS,EAAC;MAAmC;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC,eACNf,OAAA;IAAKK,SAAS,EAAC,WAAW;IAAAY,QAAA,gBACxBjB,OAAA;MAAKK,SAAS,EAAC;IAAoC;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAC1Df,OAAA;MAAKK,SAAS,EAAC;IAAqC;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAC3Df,OAAA;MAAKK,SAAS,EAAC;IAAoC;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAC1Df,OAAA;MAAKK,SAAS,EAAC;IAAoC;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACvD,CAAC,eACNf,OAAA;IAAKK,SAAS,EAAC,wCAAwC;IAAAY,QAAA,gBACrDjB,OAAA;MAAKK,SAAS,EAAC,gBAAgB;MAAAY,QAAA,gBAC7BjB,OAAA;QAAKK,SAAS,EAAC;MAAmC;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACzDf,OAAA;QAAKK,SAAS,EAAC;MAAmC;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtD,CAAC,eACNf,OAAA;MAAKK,SAAS,EAAC;IAAmC;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACtD,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACH,CACN;AAACK,GAAA,GAvBWD,gBAA0B;AAyBvC,OAAO,MAAME,eAAyB,GAAGA,CAAA,kBACvCrB,OAAA;EAAKK,SAAS,EAAC,kDAAkD;EAAAY,QAAA,eAC/DjB,OAAA;IAAKK,SAAS,EAAC,gBAAgB;IAAAY,QAAA,gBAC7BjB,OAAA;MAAKK,SAAS,EAAC;IAAuC;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAC7Df,OAAA;MAAKK,SAAS,EAAC,kBAAkB;MAAAY,QAAA,gBAC/BjB,OAAA;QAAKK,SAAS,EAAC,6BAA6B;QAAAY,QAAA,gBAC1CjB,OAAA;UAAKK,SAAS,EAAC;QAAmC;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACzDf,OAAA;UAAKK,SAAS,EAAC;QAAmC;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CAAC,eACNf,OAAA;QAAKK,SAAS,EAAC,WAAW;QAAAY,QAAA,gBACxBjB,OAAA;UAAKK,SAAS,EAAC;QAAqC;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC3Df,OAAA;UAAKK,SAAS,EAAC;QAAoC;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CAAC,eACNf,OAAA;QAAKK,SAAS,EAAC,gBAAgB;QAAAY,QAAA,gBAC7BjB,OAAA;UAAKK,SAAS,EAAC;QAAmC;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACzDf,OAAA;UAAKK,SAAS,EAAC;QAAmC;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACH,CACN;AAACO,GAAA,GApBWD,eAAyB;AAsBtC,OAAO,MAAME,kBAA4B,GAAGA,CAAA,kBAC1CvB,OAAA;EAAKK,SAAS,EAAC,2DAA2D;EAAAY,QAAA,gBAExEjB,OAAA;IAAKK,SAAS,EAAC,gBAAgB;IAAAY,QAAA,gBAC7BjB,OAAA;MAAKK,SAAS,EAAC;IAAoC;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAC1Df,OAAA;MAAKK,SAAS,EAAC,6BAA6B;MAAAY,QAAA,gBAC1CjB,OAAA;QAAKK,SAAS,EAAC;MAAyC;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC/Df,OAAA;QAAKK,SAAS,EAAC,WAAW;QAAAY,QAAA,gBACxBjB,OAAA;UAAKK,SAAS,EAAC;QAAmC;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACzDf,OAAA;UAAKK,SAAS,EAAC;QAAmC;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNf,OAAA;MAAKK,SAAS,EAAC,gBAAgB;MAAAY,QAAA,gBAC7BjB,OAAA;QAAKK,SAAS,EAAC;MAAmC;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACzDf,OAAA;QAAKK,SAAS,EAAC;MAAmC;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACzDf,OAAA;QAAKK,SAAS,EAAC;MAAmC;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC,eAGNf,OAAA;IAAKK,SAAS,EAAC;EAAuC;IAAAO,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC,eAG7Df,OAAA;IAAKK,SAAS,EAAC,WAAW;IAAAY,QAAA,EACvBO,KAAK,CAACC,IAAI,CAAC;MAAEC,MAAM,EAAE;IAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBAClC7B,OAAA;MAAaK,SAAS,EAAC,WAAW;MAAAY,QAAA,gBAChCjB,OAAA;QAAKK,SAAS,EAAC;MAAqC;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC3Df,OAAA;QAAKK,SAAS,EAAC;MAAoC;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,EACzDc,CAAC,GAAG,CAAC,KAAK,CAAC,iBAAI7B,OAAA;QAAKK,SAAS,EAAC;MAAoC;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA,GAHlEc,CAAC;MAAAjB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAIN,CACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACH,CACN;AAACe,GAAA,GAjCWP,kBAA4B;AAmCzC,OAAO,MAAMQ,mBAA6B,GAAGA,CAAA,kBAC3C/B,OAAA;EAAKK,SAAS,EAAC,2DAA2D;EAAAY,QAAA,eACxEjB,OAAA;IAAKK,SAAS,EAAC,UAAU;IAAAY,QAAA,gBACvBjB,OAAA;MAAKK,SAAS,EAAC,kCAAkC;MAAAY,QAAA,gBAC/CjB,OAAA;QAAKK,SAAS,EAAC;MAAyC;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC/Df,OAAA;QAAKK,SAAS,EAAC,WAAW;QAAAY,QAAA,gBACxBjB,OAAA;UAAKK,SAAS,EAAC;QAAmC;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACzDf,OAAA;UAAKK,SAAS,EAAC;QAAmC;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACzDf,OAAA;UAAKK,SAAS,EAAC;QAAmC;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENf,OAAA;MAAKK,SAAS,EAAC,WAAW;MAAAY,QAAA,gBACxBjB,OAAA;QAAKK,SAAS,EAAC;MAAqC;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC3Df,OAAA;QAAKK,SAAS,EAAC;MAAoC;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC1Df,OAAA;QAAKK,SAAS,EAAC;MAAoC;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvD,CAAC,eAENf,OAAA;MAAKK,SAAS,EAAC,6BAA6B;MAAAY,QAAA,EACzCO,KAAK,CAACC,IAAI,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBAClC7B,OAAA;QAAaK,SAAS,EAAC,uBAAuB;QAAAY,QAAA,gBAC5CjB,OAAA;UAAKK,SAAS,EAAC;QAA2C;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjEf,OAAA;UAAKK,SAAS,EAAC;QAA2C;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA,GAFzDc,CAAC;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAGN,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACH,CACN;AAACiB,GAAA,GA5BWD,mBAA6B;AA8B1C,eAAe9B,OAAO;AAAC,IAAAiB,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAQ,GAAA,EAAAE,GAAA;AAAAC,YAAA,CAAAf,EAAA;AAAAe,YAAA,CAAAb,GAAA;AAAAa,YAAA,CAAAX,GAAA;AAAAW,YAAA,CAAAH,GAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}