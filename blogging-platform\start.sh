#!/bin/bash

echo "Starting MERN Stack Blogging Platform..."
echo

echo "Starting MongoDB (make sure MongoDB is installed and running)"
echo

echo "Starting Backend Server..."
cd backend
npm run dev &
BACKEND_PID=$!

echo "Waiting for backend to start..."
sleep 5

echo "Starting Frontend React App..."
cd ../frontend
npm start &
FRONTEND_PID=$!

echo
echo "========================================"
echo "  MERN Stack Blogging Platform Started"
echo "========================================"
echo
echo "Backend API: http://localhost:5000"
echo "Frontend App: http://localhost:3000"
echo
echo "Press Ctrl+C to stop all services"

# Function to cleanup processes on exit
cleanup() {
    echo "Stopping services..."
    kill $BACKEND_PID 2>/dev/null
    kill $FRONTEND_PID 2>/dev/null
    exit 0
}

# Trap Ctrl+C
trap cleanup INT

# Wait for processes
wait
