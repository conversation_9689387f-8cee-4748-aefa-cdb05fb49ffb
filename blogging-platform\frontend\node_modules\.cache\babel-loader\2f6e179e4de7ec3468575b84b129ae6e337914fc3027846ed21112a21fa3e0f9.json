{"ast": null, "code": "var _jsxFileName = \"D:\\\\D Drive\\\\Projects\\\\Blogging\\\\blogging-platform\\\\frontend\\\\src\\\\pages\\\\Register.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { Helmet } from 'react-helmet-async';\nimport { useAuth } from '../context/AuthContext';\nimport { isValidEmail } from '../utils/helpers';\nimport Loading from '../components/Loading';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Register = () => {\n  _s();\n  const {\n    register,\n    isLoading\n  } = useAuth();\n  const navigate = useNavigate();\n  const [formData, setFormData] = useState({\n    username: '',\n    email: '',\n    password: '',\n    firstName: '',\n    lastName: '',\n    bio: ''\n  });\n  const [confirmPassword, setConfirmPassword] = useState('');\n  const [errors, setErrors] = useState({});\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n  };\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.firstName.trim()) {\n      newErrors.firstName = 'First name is required';\n    }\n    if (!formData.lastName.trim()) {\n      newErrors.lastName = 'Last name is required';\n    }\n    if (!formData.username.trim()) {\n      newErrors.username = 'Username is required';\n    } else if (formData.username.length < 3) {\n      newErrors.username = 'Username must be at least 3 characters';\n    } else if (!/^[a-zA-Z0-9_]+$/.test(formData.username)) {\n      newErrors.username = 'Username can only contain letters, numbers, and underscores';\n    }\n    if (!formData.email.trim()) {\n      newErrors.email = 'Email is required';\n    } else if (!isValidEmail(formData.email)) {\n      newErrors.email = 'Please enter a valid email address';\n    }\n    if (!formData.password) {\n      newErrors.password = 'Password is required';\n    } else if (formData.password.length < 6) {\n      newErrors.password = 'Password must be at least 6 characters';\n    } else if (!/^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)/.test(formData.password)) {\n      newErrors.password = 'Password must contain at least one lowercase letter, one uppercase letter, and one number';\n    }\n    if (!confirmPassword) {\n      newErrors.confirmPassword = 'Please confirm your password';\n    } else if (formData.password !== confirmPassword) {\n      newErrors.confirmPassword = 'Passwords do not match';\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!validateForm()) {\n      return;\n    }\n    try {\n      await register(formData);\n      navigate('/dashboard');\n    } catch (err) {\n      if (err.errors) {\n        const newErrors = {};\n        err.errors.forEach(error => {\n          newErrors[error.field] = error.message;\n        });\n        setErrors(newErrors);\n      } else {\n        setErrors({\n          general: err.message || 'Registration failed'\n        });\n      }\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Helmet, {\n      children: [/*#__PURE__*/_jsxDEV(\"title\", {\n        children: \"Sign Up - BlogPlatform\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        name: \"description\",\n        content: \"Create your BlogPlatform account and start sharing your stories with the world.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-md w-full space-y-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-primary-600 rounded-lg flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-white font-bold text-xl\",\n                children: \"B\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"mt-6 text-3xl font-bold text-secondary-900\",\n            children: \"Create your account\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-2 text-sm text-secondary-600\",\n            children: [\"Or\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/login\",\n              className: \"font-medium text-primary-600 hover:text-primary-500\",\n              children: \"sign in to your existing account\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          className: \"mt-8 space-y-6\",\n          onSubmit: handleSubmit,\n          children: [errors.general && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg\",\n            children: errors.general\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-2 gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"firstName\",\n                  className: \"block text-sm font-medium text-secondary-700 mb-1\",\n                  children: \"First Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 144,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  id: \"firstName\",\n                  name: \"firstName\",\n                  type: \"text\",\n                  required: true,\n                  className: `input-field ${errors.firstName ? 'border-red-300 focus:ring-red-500' : ''}`,\n                  placeholder: \"First name\",\n                  value: formData.firstName,\n                  onChange: handleChange,\n                  disabled: isLoading\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 147,\n                  columnNumber: 19\n                }, this), errors.firstName && /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mt-1 text-sm text-red-600\",\n                  children: errors.firstName\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 159,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"lastName\",\n                  className: \"block text-sm font-medium text-secondary-700 mb-1\",\n                  children: \"Last Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 164,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  id: \"lastName\",\n                  name: \"lastName\",\n                  type: \"text\",\n                  required: true,\n                  className: `input-field ${errors.lastName ? 'border-red-300 focus:ring-red-500' : ''}`,\n                  placeholder: \"Last name\",\n                  value: formData.lastName,\n                  onChange: handleChange,\n                  disabled: isLoading\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 167,\n                  columnNumber: 19\n                }, this), errors.lastName && /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mt-1 text-sm text-red-600\",\n                  children: errors.lastName\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 179,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"username\",\n                className: \"block text-sm font-medium text-secondary-700 mb-1\",\n                children: \"Username\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                id: \"username\",\n                name: \"username\",\n                type: \"text\",\n                required: true,\n                className: `input-field ${errors.username ? 'border-red-300 focus:ring-red-500' : ''}`,\n                placeholder: \"Choose a username\",\n                value: formData.username,\n                onChange: handleChange,\n                disabled: isLoading\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 17\n              }, this), errors.username && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-600\",\n                children: errors.username\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"email\",\n                className: \"block text-sm font-medium text-secondary-700 mb-1\",\n                children: \"Email Address\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                id: \"email\",\n                name: \"email\",\n                type: \"email\",\n                autoComplete: \"email\",\n                required: true,\n                className: `input-field ${errors.email ? 'border-red-300 focus:ring-red-500' : ''}`,\n                placeholder: \"Enter your email\",\n                value: formData.email,\n                onChange: handleChange,\n                disabled: isLoading\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 17\n              }, this), errors.email && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-600\",\n                children: errors.email\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"password\",\n                className: \"block text-sm font-medium text-secondary-700 mb-1\",\n                children: \"Password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                id: \"password\",\n                name: \"password\",\n                type: \"password\",\n                autoComplete: \"new-password\",\n                required: true,\n                className: `input-field ${errors.password ? 'border-red-300 focus:ring-red-500' : ''}`,\n                placeholder: \"Create a password\",\n                value: formData.password,\n                onChange: handleChange,\n                disabled: isLoading\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 17\n              }, this), errors.password && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-600\",\n                children: errors.password\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"confirmPassword\",\n                className: \"block text-sm font-medium text-secondary-700 mb-1\",\n                children: \"Confirm Password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                id: \"confirmPassword\",\n                name: \"confirmPassword\",\n                type: \"password\",\n                autoComplete: \"new-password\",\n                required: true,\n                className: `input-field ${errors.confirmPassword ? 'border-red-300 focus:ring-red-500' : ''}`,\n                placeholder: \"Confirm your password\",\n                value: confirmPassword,\n                onChange: e => {\n                  setConfirmPassword(e.target.value);\n                  if (errors.confirmPassword) {\n                    setErrors(prev => ({\n                      ...prev,\n                      confirmPassword: ''\n                    }));\n                  }\n                },\n                disabled: isLoading\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 17\n              }, this), errors.confirmPassword && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-600\",\n                children: errors.confirmPassword\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"bio\",\n                className: \"block text-sm font-medium text-secondary-700 mb-1\",\n                children: \"Bio (Optional)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                id: \"bio\",\n                name: \"bio\",\n                rows: 3,\n                className: \"input-field resize-none\",\n                placeholder: \"Tell us a bit about yourself...\",\n                value: formData.bio,\n                onChange: handleChange,\n                disabled: isLoading\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"terms\",\n              name: \"terms\",\n              type: \"checkbox\",\n              required: true,\n              className: \"h-4 w-4 text-primary-600 focus:ring-primary-500 border-secondary-300 rounded\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"terms\",\n              className: \"ml-2 block text-sm text-secondary-700\",\n              children: [\"I agree to the\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/terms\",\n                className: \"text-primary-600 hover:text-primary-500\",\n                children: \"Terms of Service\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 17\n              }, this), ' ', \"and\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/privacy\",\n                className: \"text-primary-600 hover:text-primary-500\",\n                children: \"Privacy Policy\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              disabled: isLoading,\n              className: \"w-full btn-primary flex justify-center items-center\",\n              children: isLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(Loading, {\n                  size: \"sm\",\n                  className: \"mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 317,\n                  columnNumber: 21\n                }, this), \"Creating account...\"]\n              }, void 0, true) : 'Create account'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-secondary-600\",\n              children: [\"Already have an account?\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/login\",\n                className: \"font-medium text-primary-600 hover:text-primary-500\",\n                children: \"Sign in here\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 329,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(Register, \"DZ7rX3whrRg9z6DoSQvlx3NUTpQ=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = Register;\nexport default Register;\nvar _c;\n$RefreshReg$(_c, \"Register\");", "map": {"version": 3, "names": ["React", "useState", "Link", "useNavigate", "<PERSON><PERSON><PERSON>", "useAuth", "isValidEmail", "Loading", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Register", "_s", "register", "isLoading", "navigate", "formData", "setFormData", "username", "email", "password", "firstName", "lastName", "bio", "confirmPassword", "setConfirmPassword", "errors", "setErrors", "handleChange", "e", "name", "value", "target", "prev", "validateForm", "newErrors", "trim", "length", "test", "Object", "keys", "handleSubmit", "preventDefault", "err", "for<PERSON>ach", "error", "field", "message", "general", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "content", "className", "to", "onSubmit", "htmlFor", "id", "type", "required", "placeholder", "onChange", "disabled", "autoComplete", "rows", "size", "_c", "$RefreshReg$"], "sources": ["D:/D Drive/Projects/Blogging/blogging-platform/frontend/src/pages/Register.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { Helmet } from 'react-helmet-async';\nimport { useAuth } from '../context/AuthContext';\nimport { RegisterData } from '../types';\nimport { isValidEmail } from '../utils/helpers';\nimport Loading from '../components/Loading';\n\nconst Register: React.FC = () => {\n  const { register, isLoading } = useAuth();\n  const navigate = useNavigate();\n  \n  const [formData, setFormData] = useState<RegisterData>({\n    username: '',\n    email: '',\n    password: '',\n    firstName: '',\n    lastName: '',\n    bio: '',\n  });\n  const [confirmPassword, setConfirmPassword] = useState('');\n  const [errors, setErrors] = useState<Record<string, string>>({});\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value,\n    }));\n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: '',\n      }));\n    }\n  };\n\n  const validateForm = (): boolean => {\n    const newErrors: Record<string, string> = {};\n\n    if (!formData.firstName.trim()) {\n      newErrors.firstName = 'First name is required';\n    }\n\n    if (!formData.lastName.trim()) {\n      newErrors.lastName = 'Last name is required';\n    }\n\n    if (!formData.username.trim()) {\n      newErrors.username = 'Username is required';\n    } else if (formData.username.length < 3) {\n      newErrors.username = 'Username must be at least 3 characters';\n    } else if (!/^[a-zA-Z0-9_]+$/.test(formData.username)) {\n      newErrors.username = 'Username can only contain letters, numbers, and underscores';\n    }\n\n    if (!formData.email.trim()) {\n      newErrors.email = 'Email is required';\n    } else if (!isValidEmail(formData.email)) {\n      newErrors.email = 'Please enter a valid email address';\n    }\n\n    if (!formData.password) {\n      newErrors.password = 'Password is required';\n    } else if (formData.password.length < 6) {\n      newErrors.password = 'Password must be at least 6 characters';\n    } else if (!/^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)/.test(formData.password)) {\n      newErrors.password = 'Password must contain at least one lowercase letter, one uppercase letter, and one number';\n    }\n\n    if (!confirmPassword) {\n      newErrors.confirmPassword = 'Please confirm your password';\n    } else if (formData.password !== confirmPassword) {\n      newErrors.confirmPassword = 'Passwords do not match';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!validateForm()) {\n      return;\n    }\n\n    try {\n      await register(formData);\n      navigate('/dashboard');\n    } catch (err: any) {\n      if (err.errors) {\n        const newErrors: Record<string, string> = {};\n        err.errors.forEach((error: any) => {\n          newErrors[error.field] = error.message;\n        });\n        setErrors(newErrors);\n      } else {\n        setErrors({ general: err.message || 'Registration failed' });\n      }\n    }\n  };\n\n  return (\n    <>\n      <Helmet>\n        <title>Sign Up - BlogPlatform</title>\n        <meta name=\"description\" content=\"Create your BlogPlatform account and start sharing your stories with the world.\" />\n      </Helmet>\n\n      <div className=\"min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8\">\n        <div className=\"max-w-md w-full space-y-8\">\n          <div className=\"text-center\">\n            <div className=\"flex justify-center\">\n              <div className=\"w-12 h-12 bg-primary-600 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-xl\">B</span>\n              </div>\n            </div>\n            <h2 className=\"mt-6 text-3xl font-bold text-secondary-900\">\n              Create your account\n            </h2>\n            <p className=\"mt-2 text-sm text-secondary-600\">\n              Or{' '}\n              <Link\n                to=\"/login\"\n                className=\"font-medium text-primary-600 hover:text-primary-500\"\n              >\n                sign in to your existing account\n              </Link>\n            </p>\n          </div>\n\n          <form className=\"mt-8 space-y-6\" onSubmit={handleSubmit}>\n            {errors.general && (\n              <div className=\"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg\">\n                {errors.general}\n              </div>\n            )}\n\n            <div className=\"space-y-4\">\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div>\n                  <label htmlFor=\"firstName\" className=\"block text-sm font-medium text-secondary-700 mb-1\">\n                    First Name\n                  </label>\n                  <input\n                    id=\"firstName\"\n                    name=\"firstName\"\n                    type=\"text\"\n                    required\n                    className={`input-field ${errors.firstName ? 'border-red-300 focus:ring-red-500' : ''}`}\n                    placeholder=\"First name\"\n                    value={formData.firstName}\n                    onChange={handleChange}\n                    disabled={isLoading}\n                  />\n                  {errors.firstName && (\n                    <p className=\"mt-1 text-sm text-red-600\">{errors.firstName}</p>\n                  )}\n                </div>\n\n                <div>\n                  <label htmlFor=\"lastName\" className=\"block text-sm font-medium text-secondary-700 mb-1\">\n                    Last Name\n                  </label>\n                  <input\n                    id=\"lastName\"\n                    name=\"lastName\"\n                    type=\"text\"\n                    required\n                    className={`input-field ${errors.lastName ? 'border-red-300 focus:ring-red-500' : ''}`}\n                    placeholder=\"Last name\"\n                    value={formData.lastName}\n                    onChange={handleChange}\n                    disabled={isLoading}\n                  />\n                  {errors.lastName && (\n                    <p className=\"mt-1 text-sm text-red-600\">{errors.lastName}</p>\n                  )}\n                </div>\n              </div>\n\n              <div>\n                <label htmlFor=\"username\" className=\"block text-sm font-medium text-secondary-700 mb-1\">\n                  Username\n                </label>\n                <input\n                  id=\"username\"\n                  name=\"username\"\n                  type=\"text\"\n                  required\n                  className={`input-field ${errors.username ? 'border-red-300 focus:ring-red-500' : ''}`}\n                  placeholder=\"Choose a username\"\n                  value={formData.username}\n                  onChange={handleChange}\n                  disabled={isLoading}\n                />\n                {errors.username && (\n                  <p className=\"mt-1 text-sm text-red-600\">{errors.username}</p>\n                )}\n              </div>\n\n              <div>\n                <label htmlFor=\"email\" className=\"block text-sm font-medium text-secondary-700 mb-1\">\n                  Email Address\n                </label>\n                <input\n                  id=\"email\"\n                  name=\"email\"\n                  type=\"email\"\n                  autoComplete=\"email\"\n                  required\n                  className={`input-field ${errors.email ? 'border-red-300 focus:ring-red-500' : ''}`}\n                  placeholder=\"Enter your email\"\n                  value={formData.email}\n                  onChange={handleChange}\n                  disabled={isLoading}\n                />\n                {errors.email && (\n                  <p className=\"mt-1 text-sm text-red-600\">{errors.email}</p>\n                )}\n              </div>\n\n              <div>\n                <label htmlFor=\"password\" className=\"block text-sm font-medium text-secondary-700 mb-1\">\n                  Password\n                </label>\n                <input\n                  id=\"password\"\n                  name=\"password\"\n                  type=\"password\"\n                  autoComplete=\"new-password\"\n                  required\n                  className={`input-field ${errors.password ? 'border-red-300 focus:ring-red-500' : ''}`}\n                  placeholder=\"Create a password\"\n                  value={formData.password}\n                  onChange={handleChange}\n                  disabled={isLoading}\n                />\n                {errors.password && (\n                  <p className=\"mt-1 text-sm text-red-600\">{errors.password}</p>\n                )}\n              </div>\n\n              <div>\n                <label htmlFor=\"confirmPassword\" className=\"block text-sm font-medium text-secondary-700 mb-1\">\n                  Confirm Password\n                </label>\n                <input\n                  id=\"confirmPassword\"\n                  name=\"confirmPassword\"\n                  type=\"password\"\n                  autoComplete=\"new-password\"\n                  required\n                  className={`input-field ${errors.confirmPassword ? 'border-red-300 focus:ring-red-500' : ''}`}\n                  placeholder=\"Confirm your password\"\n                  value={confirmPassword}\n                  onChange={(e) => {\n                    setConfirmPassword(e.target.value);\n                    if (errors.confirmPassword) {\n                      setErrors(prev => ({ ...prev, confirmPassword: '' }));\n                    }\n                  }}\n                  disabled={isLoading}\n                />\n                {errors.confirmPassword && (\n                  <p className=\"mt-1 text-sm text-red-600\">{errors.confirmPassword}</p>\n                )}\n              </div>\n\n              <div>\n                <label htmlFor=\"bio\" className=\"block text-sm font-medium text-secondary-700 mb-1\">\n                  Bio (Optional)\n                </label>\n                <textarea\n                  id=\"bio\"\n                  name=\"bio\"\n                  rows={3}\n                  className=\"input-field resize-none\"\n                  placeholder=\"Tell us a bit about yourself...\"\n                  value={formData.bio}\n                  onChange={handleChange}\n                  disabled={isLoading}\n                />\n              </div>\n            </div>\n\n            <div className=\"flex items-center\">\n              <input\n                id=\"terms\"\n                name=\"terms\"\n                type=\"checkbox\"\n                required\n                className=\"h-4 w-4 text-primary-600 focus:ring-primary-500 border-secondary-300 rounded\"\n              />\n              <label htmlFor=\"terms\" className=\"ml-2 block text-sm text-secondary-700\">\n                I agree to the{' '}\n                <Link to=\"/terms\" className=\"text-primary-600 hover:text-primary-500\">\n                  Terms of Service\n                </Link>{' '}\n                and{' '}\n                <Link to=\"/privacy\" className=\"text-primary-600 hover:text-primary-500\">\n                  Privacy Policy\n                </Link>\n              </label>\n            </div>\n\n            <div>\n              <button\n                type=\"submit\"\n                disabled={isLoading}\n                className=\"w-full btn-primary flex justify-center items-center\"\n              >\n                {isLoading ? (\n                  <>\n                    <Loading size=\"sm\" className=\"mr-2\" />\n                    Creating account...\n                  </>\n                ) : (\n                  'Create account'\n                )}\n              </button>\n            </div>\n\n            <div className=\"text-center\">\n              <p className=\"text-sm text-secondary-600\">\n                Already have an account?{' '}\n                <Link\n                  to=\"/login\"\n                  className=\"font-medium text-primary-600 hover:text-primary-500\"\n                >\n                  Sign in here\n                </Link>\n              </p>\n            </div>\n          </form>\n        </div>\n      </div>\n    </>\n  );\n};\n\nexport default Register;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,MAAM,QAAQ,oBAAoB;AAC3C,SAASC,OAAO,QAAQ,wBAAwB;AAEhD,SAASC,YAAY,QAAQ,kBAAkB;AAC/C,OAAOC,OAAO,MAAM,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE5C,MAAMC,QAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM;IAAEC,QAAQ;IAAEC;EAAU,CAAC,GAAGV,OAAO,CAAC,CAAC;EACzC,MAAMW,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAE9B,MAAM,CAACc,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAe;IACrDkB,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZC,GAAG,EAAE;EACP,CAAC,CAAC;EACF,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC0B,MAAM,EAAEC,SAAS,CAAC,GAAG3B,QAAQ,CAAyB,CAAC,CAAC,CAAC;EAEhE,MAAM4B,YAAY,GAAIC,CAA4D,IAAK;IACrF,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCf,WAAW,CAACgB,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;IACH;IACA,IAAIL,MAAM,CAACI,IAAI,CAAC,EAAE;MAChBH,SAAS,CAACM,IAAI,KAAK;QACjB,GAAGA,IAAI;QACP,CAACH,IAAI,GAAG;MACV,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMI,YAAY,GAAGA,CAAA,KAAe;IAClC,MAAMC,SAAiC,GAAG,CAAC,CAAC;IAE5C,IAAI,CAACnB,QAAQ,CAACK,SAAS,CAACe,IAAI,CAAC,CAAC,EAAE;MAC9BD,SAAS,CAACd,SAAS,GAAG,wBAAwB;IAChD;IAEA,IAAI,CAACL,QAAQ,CAACM,QAAQ,CAACc,IAAI,CAAC,CAAC,EAAE;MAC7BD,SAAS,CAACb,QAAQ,GAAG,uBAAuB;IAC9C;IAEA,IAAI,CAACN,QAAQ,CAACE,QAAQ,CAACkB,IAAI,CAAC,CAAC,EAAE;MAC7BD,SAAS,CAACjB,QAAQ,GAAG,sBAAsB;IAC7C,CAAC,MAAM,IAAIF,QAAQ,CAACE,QAAQ,CAACmB,MAAM,GAAG,CAAC,EAAE;MACvCF,SAAS,CAACjB,QAAQ,GAAG,wCAAwC;IAC/D,CAAC,MAAM,IAAI,CAAC,iBAAiB,CAACoB,IAAI,CAACtB,QAAQ,CAACE,QAAQ,CAAC,EAAE;MACrDiB,SAAS,CAACjB,QAAQ,GAAG,6DAA6D;IACpF;IAEA,IAAI,CAACF,QAAQ,CAACG,KAAK,CAACiB,IAAI,CAAC,CAAC,EAAE;MAC1BD,SAAS,CAAChB,KAAK,GAAG,mBAAmB;IACvC,CAAC,MAAM,IAAI,CAACd,YAAY,CAACW,QAAQ,CAACG,KAAK,CAAC,EAAE;MACxCgB,SAAS,CAAChB,KAAK,GAAG,oCAAoC;IACxD;IAEA,IAAI,CAACH,QAAQ,CAACI,QAAQ,EAAE;MACtBe,SAAS,CAACf,QAAQ,GAAG,sBAAsB;IAC7C,CAAC,MAAM,IAAIJ,QAAQ,CAACI,QAAQ,CAACiB,MAAM,GAAG,CAAC,EAAE;MACvCF,SAAS,CAACf,QAAQ,GAAG,wCAAwC;IAC/D,CAAC,MAAM,IAAI,CAAC,iCAAiC,CAACkB,IAAI,CAACtB,QAAQ,CAACI,QAAQ,CAAC,EAAE;MACrEe,SAAS,CAACf,QAAQ,GAAG,2FAA2F;IAClH;IAEA,IAAI,CAACI,eAAe,EAAE;MACpBW,SAAS,CAACX,eAAe,GAAG,8BAA8B;IAC5D,CAAC,MAAM,IAAIR,QAAQ,CAACI,QAAQ,KAAKI,eAAe,EAAE;MAChDW,SAAS,CAACX,eAAe,GAAG,wBAAwB;IACtD;IAEAG,SAAS,CAACQ,SAAS,CAAC;IACpB,OAAOI,MAAM,CAACC,IAAI,CAACL,SAAS,CAAC,CAACE,MAAM,KAAK,CAAC;EAC5C,CAAC;EAED,MAAMI,YAAY,GAAG,MAAOZ,CAAkB,IAAK;IACjDA,CAAC,CAACa,cAAc,CAAC,CAAC;IAElB,IAAI,CAACR,YAAY,CAAC,CAAC,EAAE;MACnB;IACF;IAEA,IAAI;MACF,MAAMrB,QAAQ,CAACG,QAAQ,CAAC;MACxBD,QAAQ,CAAC,YAAY,CAAC;IACxB,CAAC,CAAC,OAAO4B,GAAQ,EAAE;MACjB,IAAIA,GAAG,CAACjB,MAAM,EAAE;QACd,MAAMS,SAAiC,GAAG,CAAC,CAAC;QAC5CQ,GAAG,CAACjB,MAAM,CAACkB,OAAO,CAAEC,KAAU,IAAK;UACjCV,SAAS,CAACU,KAAK,CAACC,KAAK,CAAC,GAAGD,KAAK,CAACE,OAAO;QACxC,CAAC,CAAC;QACFpB,SAAS,CAACQ,SAAS,CAAC;MACtB,CAAC,MAAM;QACLR,SAAS,CAAC;UAAEqB,OAAO,EAAEL,GAAG,CAACI,OAAO,IAAI;QAAsB,CAAC,CAAC;MAC9D;IACF;EACF,CAAC;EAED,oBACEvC,OAAA,CAAAE,SAAA;IAAAuC,QAAA,gBACEzC,OAAA,CAACL,MAAM;MAAA8C,QAAA,gBACLzC,OAAA;QAAAyC,QAAA,EAAO;MAAsB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACrC7C,OAAA;QAAMsB,IAAI,EAAC,aAAa;QAACwB,OAAO,EAAC;MAAiF;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/G,CAAC,eAET7C,OAAA;MAAK+C,SAAS,EAAC,0EAA0E;MAAAN,QAAA,eACvFzC,OAAA;QAAK+C,SAAS,EAAC,2BAA2B;QAAAN,QAAA,gBACxCzC,OAAA;UAAK+C,SAAS,EAAC,aAAa;UAAAN,QAAA,gBAC1BzC,OAAA;YAAK+C,SAAS,EAAC,qBAAqB;YAAAN,QAAA,eAClCzC,OAAA;cAAK+C,SAAS,EAAC,sEAAsE;cAAAN,QAAA,eACnFzC,OAAA;gBAAM+C,SAAS,EAAC,8BAA8B;gBAAAN,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN7C,OAAA;YAAI+C,SAAS,EAAC,4CAA4C;YAAAN,QAAA,EAAC;UAE3D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL7C,OAAA;YAAG+C,SAAS,EAAC,iCAAiC;YAAAN,QAAA,GAAC,IAC3C,EAAC,GAAG,eACNzC,OAAA,CAACP,IAAI;cACHuD,EAAE,EAAC,QAAQ;cACXD,SAAS,EAAC,qDAAqD;cAAAN,QAAA,EAChE;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAEN7C,OAAA;UAAM+C,SAAS,EAAC,gBAAgB;UAACE,QAAQ,EAAEhB,YAAa;UAAAQ,QAAA,GACrDvB,MAAM,CAACsB,OAAO,iBACbxC,OAAA;YAAK+C,SAAS,EAAC,mEAAmE;YAAAN,QAAA,EAC/EvB,MAAM,CAACsB;UAAO;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CACN,eAED7C,OAAA;YAAK+C,SAAS,EAAC,WAAW;YAAAN,QAAA,gBACxBzC,OAAA;cAAK+C,SAAS,EAAC,wBAAwB;cAAAN,QAAA,gBACrCzC,OAAA;gBAAAyC,QAAA,gBACEzC,OAAA;kBAAOkD,OAAO,EAAC,WAAW;kBAACH,SAAS,EAAC,mDAAmD;kBAAAN,QAAA,EAAC;gBAEzF;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR7C,OAAA;kBACEmD,EAAE,EAAC,WAAW;kBACd7B,IAAI,EAAC,WAAW;kBAChB8B,IAAI,EAAC,MAAM;kBACXC,QAAQ;kBACRN,SAAS,EAAE,eAAe7B,MAAM,CAACL,SAAS,GAAG,mCAAmC,GAAG,EAAE,EAAG;kBACxFyC,WAAW,EAAC,YAAY;kBACxB/B,KAAK,EAAEf,QAAQ,CAACK,SAAU;kBAC1B0C,QAAQ,EAAEnC,YAAa;kBACvBoC,QAAQ,EAAElD;gBAAU;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC,EACD3B,MAAM,CAACL,SAAS,iBACfb,OAAA;kBAAG+C,SAAS,EAAC,2BAA2B;kBAAAN,QAAA,EAAEvB,MAAM,CAACL;gBAAS;kBAAA6B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAC/D;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN7C,OAAA;gBAAAyC,QAAA,gBACEzC,OAAA;kBAAOkD,OAAO,EAAC,UAAU;kBAACH,SAAS,EAAC,mDAAmD;kBAAAN,QAAA,EAAC;gBAExF;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR7C,OAAA;kBACEmD,EAAE,EAAC,UAAU;kBACb7B,IAAI,EAAC,UAAU;kBACf8B,IAAI,EAAC,MAAM;kBACXC,QAAQ;kBACRN,SAAS,EAAE,eAAe7B,MAAM,CAACJ,QAAQ,GAAG,mCAAmC,GAAG,EAAE,EAAG;kBACvFwC,WAAW,EAAC,WAAW;kBACvB/B,KAAK,EAAEf,QAAQ,CAACM,QAAS;kBACzByC,QAAQ,EAAEnC,YAAa;kBACvBoC,QAAQ,EAAElD;gBAAU;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC,EACD3B,MAAM,CAACJ,QAAQ,iBACdd,OAAA;kBAAG+C,SAAS,EAAC,2BAA2B;kBAAAN,QAAA,EAAEvB,MAAM,CAACJ;gBAAQ;kBAAA4B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAC9D;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN7C,OAAA;cAAAyC,QAAA,gBACEzC,OAAA;gBAAOkD,OAAO,EAAC,UAAU;gBAACH,SAAS,EAAC,mDAAmD;gBAAAN,QAAA,EAAC;cAExF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR7C,OAAA;gBACEmD,EAAE,EAAC,UAAU;gBACb7B,IAAI,EAAC,UAAU;gBACf8B,IAAI,EAAC,MAAM;gBACXC,QAAQ;gBACRN,SAAS,EAAE,eAAe7B,MAAM,CAACR,QAAQ,GAAG,mCAAmC,GAAG,EAAE,EAAG;gBACvF4C,WAAW,EAAC,mBAAmB;gBAC/B/B,KAAK,EAAEf,QAAQ,CAACE,QAAS;gBACzB6C,QAAQ,EAAEnC,YAAa;gBACvBoC,QAAQ,EAAElD;cAAU;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC,EACD3B,MAAM,CAACR,QAAQ,iBACdV,OAAA;gBAAG+C,SAAS,EAAC,2BAA2B;gBAAAN,QAAA,EAAEvB,MAAM,CAACR;cAAQ;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAC9D;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEN7C,OAAA;cAAAyC,QAAA,gBACEzC,OAAA;gBAAOkD,OAAO,EAAC,OAAO;gBAACH,SAAS,EAAC,mDAAmD;gBAAAN,QAAA,EAAC;cAErF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR7C,OAAA;gBACEmD,EAAE,EAAC,OAAO;gBACV7B,IAAI,EAAC,OAAO;gBACZ8B,IAAI,EAAC,OAAO;gBACZK,YAAY,EAAC,OAAO;gBACpBJ,QAAQ;gBACRN,SAAS,EAAE,eAAe7B,MAAM,CAACP,KAAK,GAAG,mCAAmC,GAAG,EAAE,EAAG;gBACpF2C,WAAW,EAAC,kBAAkB;gBAC9B/B,KAAK,EAAEf,QAAQ,CAACG,KAAM;gBACtB4C,QAAQ,EAAEnC,YAAa;gBACvBoC,QAAQ,EAAElD;cAAU;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC,EACD3B,MAAM,CAACP,KAAK,iBACXX,OAAA;gBAAG+C,SAAS,EAAC,2BAA2B;gBAAAN,QAAA,EAAEvB,MAAM,CAACP;cAAK;gBAAA+B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAC3D;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEN7C,OAAA;cAAAyC,QAAA,gBACEzC,OAAA;gBAAOkD,OAAO,EAAC,UAAU;gBAACH,SAAS,EAAC,mDAAmD;gBAAAN,QAAA,EAAC;cAExF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR7C,OAAA;gBACEmD,EAAE,EAAC,UAAU;gBACb7B,IAAI,EAAC,UAAU;gBACf8B,IAAI,EAAC,UAAU;gBACfK,YAAY,EAAC,cAAc;gBAC3BJ,QAAQ;gBACRN,SAAS,EAAE,eAAe7B,MAAM,CAACN,QAAQ,GAAG,mCAAmC,GAAG,EAAE,EAAG;gBACvF0C,WAAW,EAAC,mBAAmB;gBAC/B/B,KAAK,EAAEf,QAAQ,CAACI,QAAS;gBACzB2C,QAAQ,EAAEnC,YAAa;gBACvBoC,QAAQ,EAAElD;cAAU;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC,EACD3B,MAAM,CAACN,QAAQ,iBACdZ,OAAA;gBAAG+C,SAAS,EAAC,2BAA2B;gBAAAN,QAAA,EAAEvB,MAAM,CAACN;cAAQ;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAC9D;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEN7C,OAAA;cAAAyC,QAAA,gBACEzC,OAAA;gBAAOkD,OAAO,EAAC,iBAAiB;gBAACH,SAAS,EAAC,mDAAmD;gBAAAN,QAAA,EAAC;cAE/F;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR7C,OAAA;gBACEmD,EAAE,EAAC,iBAAiB;gBACpB7B,IAAI,EAAC,iBAAiB;gBACtB8B,IAAI,EAAC,UAAU;gBACfK,YAAY,EAAC,cAAc;gBAC3BJ,QAAQ;gBACRN,SAAS,EAAE,eAAe7B,MAAM,CAACF,eAAe,GAAG,mCAAmC,GAAG,EAAE,EAAG;gBAC9FsC,WAAW,EAAC,uBAAuB;gBACnC/B,KAAK,EAAEP,eAAgB;gBACvBuC,QAAQ,EAAGlC,CAAC,IAAK;kBACfJ,kBAAkB,CAACI,CAAC,CAACG,MAAM,CAACD,KAAK,CAAC;kBAClC,IAAIL,MAAM,CAACF,eAAe,EAAE;oBAC1BG,SAAS,CAACM,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAET,eAAe,EAAE;oBAAG,CAAC,CAAC,CAAC;kBACvD;gBACF,CAAE;gBACFwC,QAAQ,EAAElD;cAAU;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC,EACD3B,MAAM,CAACF,eAAe,iBACrBhB,OAAA;gBAAG+C,SAAS,EAAC,2BAA2B;gBAAAN,QAAA,EAAEvB,MAAM,CAACF;cAAe;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CACrE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEN7C,OAAA;cAAAyC,QAAA,gBACEzC,OAAA;gBAAOkD,OAAO,EAAC,KAAK;gBAACH,SAAS,EAAC,mDAAmD;gBAAAN,QAAA,EAAC;cAEnF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR7C,OAAA;gBACEmD,EAAE,EAAC,KAAK;gBACR7B,IAAI,EAAC,KAAK;gBACVoC,IAAI,EAAE,CAAE;gBACRX,SAAS,EAAC,yBAAyB;gBACnCO,WAAW,EAAC,iCAAiC;gBAC7C/B,KAAK,EAAEf,QAAQ,CAACO,GAAI;gBACpBwC,QAAQ,EAAEnC,YAAa;gBACvBoC,QAAQ,EAAElD;cAAU;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN7C,OAAA;YAAK+C,SAAS,EAAC,mBAAmB;YAAAN,QAAA,gBAChCzC,OAAA;cACEmD,EAAE,EAAC,OAAO;cACV7B,IAAI,EAAC,OAAO;cACZ8B,IAAI,EAAC,UAAU;cACfC,QAAQ;cACRN,SAAS,EAAC;YAA8E;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzF,CAAC,eACF7C,OAAA;cAAOkD,OAAO,EAAC,OAAO;cAACH,SAAS,EAAC,uCAAuC;cAAAN,QAAA,GAAC,gBACzD,EAAC,GAAG,eAClBzC,OAAA,CAACP,IAAI;gBAACuD,EAAE,EAAC,QAAQ;gBAACD,SAAS,EAAC,yCAAyC;gBAAAN,QAAA,EAAC;cAEtE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EAAC,GAAG,EAAC,KACT,EAAC,GAAG,eACP7C,OAAA,CAACP,IAAI;gBAACuD,EAAE,EAAC,UAAU;gBAACD,SAAS,EAAC,yCAAyC;gBAAAN,QAAA,EAAC;cAExE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAEN7C,OAAA;YAAAyC,QAAA,eACEzC,OAAA;cACEoD,IAAI,EAAC,QAAQ;cACbI,QAAQ,EAAElD,SAAU;cACpByC,SAAS,EAAC,qDAAqD;cAAAN,QAAA,EAE9DnC,SAAS,gBACRN,OAAA,CAAAE,SAAA;gBAAAuC,QAAA,gBACEzC,OAAA,CAACF,OAAO;kBAAC6D,IAAI,EAAC,IAAI;kBAACZ,SAAS,EAAC;gBAAM;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,uBAExC;cAAA,eAAE,CAAC,GAEH;YACD;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEN7C,OAAA;YAAK+C,SAAS,EAAC,aAAa;YAAAN,QAAA,eAC1BzC,OAAA;cAAG+C,SAAS,EAAC,4BAA4B;cAAAN,QAAA,GAAC,0BAChB,EAAC,GAAG,eAC5BzC,OAAA,CAACP,IAAI;gBACHuD,EAAE,EAAC,QAAQ;gBACXD,SAAS,EAAC,qDAAqD;gBAAAN,QAAA,EAChE;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAACzC,EAAA,CA7UID,QAAkB;EAAA,QACUP,OAAO,EACtBF,WAAW;AAAA;AAAAkE,EAAA,GAFxBzD,QAAkB;AA+UxB,eAAeA,QAAQ;AAAC,IAAAyD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}