{"ast": null, "code": "var _jsxFileName = \"D:\\\\D Drive\\\\Projects\\\\Blogging\\\\blogging-platform\\\\frontend\\\\src\\\\pages\\\\Profile.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Helmet } from 'react-helmet-async';\nimport { useAuth } from '../context/AuthContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Profile = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Helmet, {\n      children: [/*#__PURE__*/_jsxDEV(\"title\", {\n        children: \"Profile - BlogPlatform\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        name: \"description\",\n        content: \"Manage your profile information and settings.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-3xl font-bold text-secondary-900 mb-8\",\n        children: \"Profile\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card p-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-6 mb-8\",\n          children: [user !== null && user !== void 0 && user.avatar ? /*#__PURE__*/_jsxDEV(\"img\", {\n            src: user.avatar,\n            alt: user.fullName,\n            className: \"w-24 h-24 rounded-full object-cover\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-24 h-24 bg-primary-600 rounded-full flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-white text-2xl font-bold\",\n              children: user ? user.firstName.charAt(0) + user.lastName.charAt(0) : 'U'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 28,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 27,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-secondary-900\",\n              children: user === null || user === void 0 ? void 0 : user.fullName\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 34,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-secondary-600\",\n              children: [\"@\", user === null || user === void 0 ? void 0 : user.username]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 35,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-secondary-600\",\n              children: user === null || user === void 0 ? void 0 : user.email\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 36,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 33,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-secondary-700 mb-1\",\n              children: \"Bio\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 42,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-secondary-900\",\n              children: (user === null || user === void 0 ? void 0 : user.bio) || 'No bio provided'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 43,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-secondary-700 mb-1\",\n              children: \"Role\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 47,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `px-2 py-1 text-xs font-medium rounded-full ${(user === null || user === void 0 ? void 0 : user.role) === 'admin' ? 'bg-purple-100 text-purple-800' : 'bg-blue-100 text-blue-800'}`,\n              children: user === null || user === void 0 ? void 0 : user.role\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 48,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-secondary-700 mb-1\",\n              children: \"Member Since\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 58,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-secondary-900\",\n              children: user !== null && user !== void 0 && user.createdAt ? new Date(user.createdAt).toLocaleDateString() : 'Unknown'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-8 pt-8 border-t border-secondary-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn-primary mr-4\",\n            children: \"Edit Profile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn-outline\",\n            children: \"Change Password\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(Profile, \"9ep4vdl3mBfipxjmc+tQCDhw6Ik=\", false, function () {\n  return [useAuth];\n});\n_c = Profile;\nexport default Profile;\nvar _c;\n$RefreshReg$(_c, \"Profile\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON>", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Profile", "_s", "user", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "content", "className", "avatar", "src", "alt", "fullName", "firstName", "char<PERSON>t", "lastName", "username", "email", "bio", "role", "createdAt", "Date", "toLocaleDateString", "_c", "$RefreshReg$"], "sources": ["D:/D Drive/Projects/Blogging/blogging-platform/frontend/src/pages/Profile.tsx"], "sourcesContent": ["import React from 'react';\nimport { Helmet } from 'react-helmet-async';\nimport { useAuth } from '../context/AuthContext';\n\nconst Profile: React.FC = () => {\n  const { user } = useAuth();\n\n  return (\n    <>\n      <Helmet>\n        <title>Profile - BlogPlatform</title>\n        <meta name=\"description\" content=\"Manage your profile information and settings.\" />\n      </Helmet>\n\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <h1 className=\"text-3xl font-bold text-secondary-900 mb-8\">Profile</h1>\n        \n        <div className=\"card p-8\">\n          <div className=\"flex items-center space-x-6 mb-8\">\n            {user?.avatar ? (\n              <img\n                src={user.avatar}\n                alt={user.fullName}\n                className=\"w-24 h-24 rounded-full object-cover\"\n              />\n            ) : (\n              <div className=\"w-24 h-24 bg-primary-600 rounded-full flex items-center justify-center\">\n                <span className=\"text-white text-2xl font-bold\">\n                  {user ? user.firstName.charAt(0) + user.lastName.charAt(0) : 'U'}\n                </span>\n              </div>\n            )}\n            <div>\n              <h2 className=\"text-2xl font-bold text-secondary-900\">{user?.fullName}</h2>\n              <p className=\"text-secondary-600\">@{user?.username}</p>\n              <p className=\"text-secondary-600\">{user?.email}</p>\n            </div>\n          </div>\n          \n          <div className=\"space-y-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-secondary-700 mb-1\">Bio</label>\n              <p className=\"text-secondary-900\">{user?.bio || 'No bio provided'}</p>\n            </div>\n            \n            <div>\n              <label className=\"block text-sm font-medium text-secondary-700 mb-1\">Role</label>\n              <span className={`px-2 py-1 text-xs font-medium rounded-full ${\n                user?.role === 'admin' \n                  ? 'bg-purple-100 text-purple-800' \n                  : 'bg-blue-100 text-blue-800'\n              }`}>\n                {user?.role}\n              </span>\n            </div>\n            \n            <div>\n              <label className=\"block text-sm font-medium text-secondary-700 mb-1\">Member Since</label>\n              <p className=\"text-secondary-900\">\n                {user?.createdAt ? new Date(user.createdAt).toLocaleDateString() : 'Unknown'}\n              </p>\n            </div>\n          </div>\n          \n          <div className=\"mt-8 pt-8 border-t border-secondary-200\">\n            <button className=\"btn-primary mr-4\">Edit Profile</button>\n            <button className=\"btn-outline\">Change Password</button>\n          </div>\n        </div>\n      </div>\n    </>\n  );\n};\n\nexport default Profile;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,oBAAoB;AAC3C,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEjD,MAAMC,OAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAM;IAAEC;EAAK,CAAC,GAAGP,OAAO,CAAC,CAAC;EAE1B,oBACEE,OAAA,CAAAE,SAAA;IAAAI,QAAA,gBACEN,OAAA,CAACH,MAAM;MAAAS,QAAA,gBACLN,OAAA;QAAAM,QAAA,EAAO;MAAsB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACrCV,OAAA;QAAMW,IAAI,EAAC,aAAa;QAACC,OAAO,EAAC;MAA+C;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7E,CAAC,eAETV,OAAA;MAAKa,SAAS,EAAC,6CAA6C;MAAAP,QAAA,gBAC1DN,OAAA;QAAIa,SAAS,EAAC,4CAA4C;QAAAP,QAAA,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAEvEV,OAAA;QAAKa,SAAS,EAAC,UAAU;QAAAP,QAAA,gBACvBN,OAAA;UAAKa,SAAS,EAAC,kCAAkC;UAAAP,QAAA,GAC9CD,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAES,MAAM,gBACXd,OAAA;YACEe,GAAG,EAAEV,IAAI,CAACS,MAAO;YACjBE,GAAG,EAAEX,IAAI,CAACY,QAAS;YACnBJ,SAAS,EAAC;UAAqC;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC,gBAEFV,OAAA;YAAKa,SAAS,EAAC,wEAAwE;YAAAP,QAAA,eACrFN,OAAA;cAAMa,SAAS,EAAC,+BAA+B;cAAAP,QAAA,EAC5CD,IAAI,GAAGA,IAAI,CAACa,SAAS,CAACC,MAAM,CAAC,CAAC,CAAC,GAAGd,IAAI,CAACe,QAAQ,CAACD,MAAM,CAAC,CAAC,CAAC,GAAG;YAAG;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CACN,eACDV,OAAA;YAAAM,QAAA,gBACEN,OAAA;cAAIa,SAAS,EAAC,uCAAuC;cAAAP,QAAA,EAAED,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEY;YAAQ;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC3EV,OAAA;cAAGa,SAAS,EAAC,oBAAoB;cAAAP,QAAA,GAAC,GAAC,EAACD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgB,QAAQ;YAAA;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvDV,OAAA;cAAGa,SAAS,EAAC,oBAAoB;cAAAP,QAAA,EAAED,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiB;YAAK;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENV,OAAA;UAAKa,SAAS,EAAC,WAAW;UAAAP,QAAA,gBACxBN,OAAA;YAAAM,QAAA,gBACEN,OAAA;cAAOa,SAAS,EAAC,mDAAmD;cAAAP,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAChFV,OAAA;cAAGa,SAAS,EAAC,oBAAoB;cAAAP,QAAA,EAAE,CAAAD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,GAAG,KAAI;YAAiB;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC,eAENV,OAAA;YAAAM,QAAA,gBACEN,OAAA;cAAOa,SAAS,EAAC,mDAAmD;cAAAP,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACjFV,OAAA;cAAMa,SAAS,EAAE,8CACf,CAAAR,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmB,IAAI,MAAK,OAAO,GAClB,+BAA+B,GAC/B,2BAA2B,EAC9B;cAAAlB,QAAA,EACAD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmB;YAAI;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAENV,OAAA;YAAAM,QAAA,gBACEN,OAAA;cAAOa,SAAS,EAAC,mDAAmD;cAAAP,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACzFV,OAAA;cAAGa,SAAS,EAAC,oBAAoB;cAAAP,QAAA,EAC9BD,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEoB,SAAS,GAAG,IAAIC,IAAI,CAACrB,IAAI,CAACoB,SAAS,CAAC,CAACE,kBAAkB,CAAC,CAAC,GAAG;YAAS;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENV,OAAA;UAAKa,SAAS,EAAC,yCAAyC;UAAAP,QAAA,gBACtDN,OAAA;YAAQa,SAAS,EAAC,kBAAkB;YAAAP,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC1DV,OAAA;YAAQa,SAAS,EAAC,aAAa;YAAAP,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAACN,EAAA,CApEID,OAAiB;EAAA,QACJL,OAAO;AAAA;AAAA8B,EAAA,GADpBzB,OAAiB;AAsEvB,eAAeA,OAAO;AAAC,IAAAyB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}