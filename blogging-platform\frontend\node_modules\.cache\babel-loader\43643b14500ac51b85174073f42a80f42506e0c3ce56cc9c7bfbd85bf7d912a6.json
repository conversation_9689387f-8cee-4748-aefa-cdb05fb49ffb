{"ast": null, "code": "var _jsxFileName = \"D:\\\\D Drive\\\\Projects\\\\Blogging\\\\blogging-platform\\\\frontend\\\\src\\\\components\\\\Header.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link, useNavigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport { getInitials } from '../utils/helpers';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Header = () => {\n  _s();\n  const {\n    user,\n    isAuthenticated,\n    logout\n  } = useAuth();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [isProfileMenuOpen, setIsProfileMenuOpen] = useState(false);\n  const handleLogout = () => {\n    logout();\n    navigate('/');\n    setIsProfileMenuOpen(false);\n  };\n  const isActivePath = path => {\n    return location.pathname === path;\n  };\n  const navLinks = [{\n    path: '/',\n    label: 'Home'\n  }, {\n    path: '/blogs',\n    label: 'Blogs'\n  }, {\n    path: '/about',\n    label: 'About'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    className: \"bg-white shadow-sm border-b border-secondary-200 sticky top-0 z-50\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center h-16\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-shrink-0\",\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-white font-bold text-lg\",\n                children: \"B\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 37,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 36,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"ml-2 text-xl font-bold text-secondary-900\",\n              children: \"BlogPlatform\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 39,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n          className: \"hidden md:flex space-x-8\",\n          children: navLinks.map(link => /*#__PURE__*/_jsxDEV(Link, {\n            to: link.path,\n            className: `px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${isActivePath(link.path) ? 'text-primary-600 bg-primary-50' : 'text-secondary-700 hover:text-primary-600 hover:bg-secondary-50'}`,\n            children: link.label\n          }, link.path, false, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden md:flex items-center space-x-4\",\n          children: isAuthenticated ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/dashboard\",\n              className: \"btn-outline text-sm\",\n              children: \"Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 64,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setIsProfileMenuOpen(!isProfileMenuOpen),\n                className: \"flex items-center space-x-2 p-2 rounded-lg hover:bg-secondary-50 transition-colors duration-200\",\n                children: [user !== null && user !== void 0 && user.avatar ? /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: user.avatar,\n                  alt: user.fullName,\n                  className: \"w-8 h-8 rounded-full object-cover\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 78,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-white text-sm font-medium\",\n                    children: user ? getInitials(user.firstName, user.lastName) : 'U'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 85,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 84,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm font-medium text-secondary-700\",\n                  children: user === null || user === void 0 ? void 0 : user.firstName\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 90,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: `w-4 h-4 text-secondary-500 transition-transform duration-200 ${isProfileMenuOpen ? 'rotate-180' : ''}`,\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M19 9l-7 7-7-7\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 101,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 93,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 73,\n                columnNumber: 19\n              }, this), isProfileMenuOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-secondary-200 py-1 z-50\",\n                children: [/*#__PURE__*/_jsxDEV(Link, {\n                  to: \"/profile\",\n                  className: \"block px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-50\",\n                  onClick: () => setIsProfileMenuOpen(false),\n                  children: \"Profile\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 108,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Link, {\n                  to: \"/dashboard/blogs\",\n                  className: \"block px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-50\",\n                  onClick: () => setIsProfileMenuOpen(false),\n                  children: \"My Blogs\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 115,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Link, {\n                  to: \"/dashboard/create\",\n                  className: \"block px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-50\",\n                  onClick: () => setIsProfileMenuOpen(false),\n                  children: \"Write Blog\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 122,\n                  columnNumber: 23\n                }, this), (user === null || user === void 0 ? void 0 : user.role) === 'admin' && /*#__PURE__*/_jsxDEV(Link, {\n                  to: \"/admin\",\n                  className: \"block px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-50\",\n                  onClick: () => setIsProfileMenuOpen(false),\n                  children: \"Admin Panel\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 130,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n                  className: \"my-1 border-secondary-200\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 138,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleLogout,\n                  className: \"block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50\",\n                  children: \"Sign Out\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 139,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 107,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 72,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/login\",\n              className: \"text-secondary-700 hover:text-primary-600 font-medium text-sm transition-colors duration-200\",\n              children: \"Sign In\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/register\",\n              className: \"btn-primary text-sm\",\n              children: \"Sign Up\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"md:hidden\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setIsMenuOpen(!isMenuOpen),\n            className: \"p-2 rounded-md text-secondary-700 hover:text-primary-600 hover:bg-secondary-50 transition-colors duration-200\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-6 h-6\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: isMenuOpen ? /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M6 18L18 6M6 6l12 12\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M4 6h16M4 12h16M4 18h16\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 9\n      }, this), isMenuOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"md:hidden border-t border-secondary-200 py-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-2\",\n          children: [navLinks.map(link => /*#__PURE__*/_jsxDEV(Link, {\n            to: link.path,\n            className: `block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200 ${isActivePath(link.path) ? 'text-primary-600 bg-primary-50' : 'text-secondary-700 hover:text-primary-600 hover:bg-secondary-50'}`,\n            onClick: () => setIsMenuOpen(false),\n            children: link.label\n          }, link.path, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 17\n          }, this)), isAuthenticated ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"hr\", {\n              className: \"my-2 border-secondary-200\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/dashboard\",\n              className: \"block px-3 py-2 rounded-md text-base font-medium text-secondary-700 hover:text-primary-600 hover:bg-secondary-50\",\n              onClick: () => setIsMenuOpen(false),\n              children: \"Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/profile\",\n              className: \"block px-3 py-2 rounded-md text-base font-medium text-secondary-700 hover:text-primary-600 hover:bg-secondary-50\",\n              onClick: () => setIsMenuOpen(false),\n              children: \"Profile\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 19\n            }, this), (user === null || user === void 0 ? void 0 : user.role) === 'admin' && /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/admin\",\n              className: \"block px-3 py-2 rounded-md text-base font-medium text-secondary-700 hover:text-primary-600 hover:bg-secondary-50\",\n              onClick: () => setIsMenuOpen(false),\n              children: \"Admin Panel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleLogout,\n              className: \"block w-full text-left px-3 py-2 rounded-md text-base font-medium text-red-600 hover:bg-red-50\",\n              children: \"Sign Out\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"hr\", {\n              className: \"my-2 border-secondary-200\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/login\",\n              className: \"block px-3 py-2 rounded-md text-base font-medium text-secondary-700 hover:text-primary-600 hover:bg-secondary-50\",\n              onClick: () => setIsMenuOpen(false),\n              children: \"Sign In\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/register\",\n              className: \"block px-3 py-2 rounded-md text-base font-medium bg-primary-600 text-white hover:bg-primary-700\",\n              onClick: () => setIsMenuOpen(false),\n              children: \"Sign Up\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 7\n    }, this), isProfileMenuOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 z-40\",\n      onClick: () => setIsProfileMenuOpen(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 267,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 30,\n    columnNumber: 5\n  }, this);\n};\n_s(Header, \"GTSEbgmBLBpAL7L+36vRQcA15Y4=\", false, function () {\n  return [useAuth, useNavigate, useLocation];\n});\n_c = Header;\nexport default Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");", "map": {"version": 3, "names": ["React", "useState", "Link", "useNavigate", "useLocation", "useAuth", "getInitials", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Header", "_s", "user", "isAuthenticated", "logout", "navigate", "location", "isMenuOpen", "setIsMenuOpen", "isProfileMenuOpen", "setIsProfileMenuOpen", "handleLogout", "isActivePath", "path", "pathname", "navLinks", "label", "className", "children", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "link", "onClick", "avatar", "src", "alt", "fullName", "firstName", "lastName", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "role", "_c", "$RefreshReg$"], "sources": ["D:/D Drive/Projects/Blogging/blogging-platform/frontend/src/components/Header.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link, useNavigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport { getInitials } from '../utils/helpers';\n\nconst Header: React.FC = () => {\n  const { user, isAuthenticated, logout } = useAuth();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [isProfileMenuOpen, setIsProfileMenuOpen] = useState(false);\n\n  const handleLogout = () => {\n    logout();\n    navigate('/');\n    setIsProfileMenuOpen(false);\n  };\n\n  const isActivePath = (path: string) => {\n    return location.pathname === path;\n  };\n\n  const navLinks = [\n    { path: '/', label: 'Home' },\n    { path: '/blogs', label: 'Blogs' },\n    { path: '/about', label: 'About' },\n  ];\n\n  return (\n    <header className=\"bg-white shadow-sm border-b border-secondary-200 sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <div className=\"flex-shrink-0\">\n            <Link to=\"/\" className=\"flex items-center\">\n              <div className=\"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-lg\">B</span>\n              </div>\n              <span className=\"ml-2 text-xl font-bold text-secondary-900\">BlogPlatform</span>\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex space-x-8\">\n            {navLinks.map((link) => (\n              <Link\n                key={link.path}\n                to={link.path}\n                className={`px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${\n                  isActivePath(link.path)\n                    ? 'text-primary-600 bg-primary-50'\n                    : 'text-secondary-700 hover:text-primary-600 hover:bg-secondary-50'\n                }`}\n              >\n                {link.label}\n              </Link>\n            ))}\n          </nav>\n\n          {/* Desktop Auth Section */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            {isAuthenticated ? (\n              <>\n                <Link\n                  to=\"/dashboard\"\n                  className=\"btn-outline text-sm\"\n                >\n                  Dashboard\n                </Link>\n                \n                {/* Profile Dropdown */}\n                <div className=\"relative\">\n                  <button\n                    onClick={() => setIsProfileMenuOpen(!isProfileMenuOpen)}\n                    className=\"flex items-center space-x-2 p-2 rounded-lg hover:bg-secondary-50 transition-colors duration-200\"\n                  >\n                    {user?.avatar ? (\n                      <img\n                        src={user.avatar}\n                        alt={user.fullName}\n                        className=\"w-8 h-8 rounded-full object-cover\"\n                      />\n                    ) : (\n                      <div className=\"w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center\">\n                        <span className=\"text-white text-sm font-medium\">\n                          {user ? getInitials(user.firstName, user.lastName) : 'U'}\n                        </span>\n                      </div>\n                    )}\n                    <span className=\"text-sm font-medium text-secondary-700\">\n                      {user?.firstName}\n                    </span>\n                    <svg\n                      className={`w-4 h-4 text-secondary-500 transition-transform duration-200 ${\n                        isProfileMenuOpen ? 'rotate-180' : ''\n                      }`}\n                      fill=\"none\"\n                      stroke=\"currentColor\"\n                      viewBox=\"0 0 24 24\"\n                    >\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n                    </svg>\n                  </button>\n\n                  {/* Profile Dropdown Menu */}\n                  {isProfileMenuOpen && (\n                    <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-secondary-200 py-1 z-50\">\n                      <Link\n                        to=\"/profile\"\n                        className=\"block px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-50\"\n                        onClick={() => setIsProfileMenuOpen(false)}\n                      >\n                        Profile\n                      </Link>\n                      <Link\n                        to=\"/dashboard/blogs\"\n                        className=\"block px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-50\"\n                        onClick={() => setIsProfileMenuOpen(false)}\n                      >\n                        My Blogs\n                      </Link>\n                      <Link\n                        to=\"/dashboard/create\"\n                        className=\"block px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-50\"\n                        onClick={() => setIsProfileMenuOpen(false)}\n                      >\n                        Write Blog\n                      </Link>\n                      {user?.role === 'admin' && (\n                        <Link\n                          to=\"/admin\"\n                          className=\"block px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-50\"\n                          onClick={() => setIsProfileMenuOpen(false)}\n                        >\n                          Admin Panel\n                        </Link>\n                      )}\n                      <hr className=\"my-1 border-secondary-200\" />\n                      <button\n                        onClick={handleLogout}\n                        className=\"block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50\"\n                      >\n                        Sign Out\n                      </button>\n                    </div>\n                  )}\n                </div>\n              </>\n            ) : (\n              <div className=\"flex items-center space-x-3\">\n                <Link\n                  to=\"/login\"\n                  className=\"text-secondary-700 hover:text-primary-600 font-medium text-sm transition-colors duration-200\"\n                >\n                  Sign In\n                </Link>\n                <Link\n                  to=\"/register\"\n                  className=\"btn-primary text-sm\"\n                >\n                  Sign Up\n                </Link>\n              </div>\n            )}\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"p-2 rounded-md text-secondary-700 hover:text-primary-600 hover:bg-secondary-50 transition-colors duration-200\"\n            >\n              <svg\n                className=\"w-6 h-6\"\n                fill=\"none\"\n                stroke=\"currentColor\"\n                viewBox=\"0 0 24 24\"\n              >\n                {isMenuOpen ? (\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                ) : (\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n                )}\n              </svg>\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden border-t border-secondary-200 py-4\">\n            <div className=\"space-y-2\">\n              {navLinks.map((link) => (\n                <Link\n                  key={link.path}\n                  to={link.path}\n                  className={`block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200 ${\n                    isActivePath(link.path)\n                      ? 'text-primary-600 bg-primary-50'\n                      : 'text-secondary-700 hover:text-primary-600 hover:bg-secondary-50'\n                  }`}\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  {link.label}\n                </Link>\n              ))}\n              \n              {isAuthenticated ? (\n                <>\n                  <hr className=\"my-2 border-secondary-200\" />\n                  <Link\n                    to=\"/dashboard\"\n                    className=\"block px-3 py-2 rounded-md text-base font-medium text-secondary-700 hover:text-primary-600 hover:bg-secondary-50\"\n                    onClick={() => setIsMenuOpen(false)}\n                  >\n                    Dashboard\n                  </Link>\n                  <Link\n                    to=\"/profile\"\n                    className=\"block px-3 py-2 rounded-md text-base font-medium text-secondary-700 hover:text-primary-600 hover:bg-secondary-50\"\n                    onClick={() => setIsMenuOpen(false)}\n                  >\n                    Profile\n                  </Link>\n                  {user?.role === 'admin' && (\n                    <Link\n                      to=\"/admin\"\n                      className=\"block px-3 py-2 rounded-md text-base font-medium text-secondary-700 hover:text-primary-600 hover:bg-secondary-50\"\n                      onClick={() => setIsMenuOpen(false)}\n                    >\n                      Admin Panel\n                    </Link>\n                  )}\n                  <button\n                    onClick={handleLogout}\n                    className=\"block w-full text-left px-3 py-2 rounded-md text-base font-medium text-red-600 hover:bg-red-50\"\n                  >\n                    Sign Out\n                  </button>\n                </>\n              ) : (\n                <>\n                  <hr className=\"my-2 border-secondary-200\" />\n                  <Link\n                    to=\"/login\"\n                    className=\"block px-3 py-2 rounded-md text-base font-medium text-secondary-700 hover:text-primary-600 hover:bg-secondary-50\"\n                    onClick={() => setIsMenuOpen(false)}\n                  >\n                    Sign In\n                  </Link>\n                  <Link\n                    to=\"/register\"\n                    className=\"block px-3 py-2 rounded-md text-base font-medium bg-primary-600 text-white hover:bg-primary-700\"\n                    onClick={() => setIsMenuOpen(false)}\n                  >\n                    Sign Up\n                  </Link>\n                </>\n              )}\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Backdrop for profile menu */}\n      {isProfileMenuOpen && (\n        <div\n          className=\"fixed inset-0 z-40\"\n          onClick={() => setIsProfileMenuOpen(false)}\n        />\n      )}\n    </header>\n  );\n};\n\nexport default Header;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACjE,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE/C,MAAMC,MAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM;IAAEC,IAAI;IAAEC,eAAe;IAAEC;EAAO,CAAC,GAAGV,OAAO,CAAC,CAAC;EACnD,MAAMW,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAC9B,MAAMc,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACc,UAAU,EAAEC,aAAa,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACmB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EAEjE,MAAMqB,YAAY,GAAGA,CAAA,KAAM;IACzBP,MAAM,CAAC,CAAC;IACRC,QAAQ,CAAC,GAAG,CAAC;IACbK,oBAAoB,CAAC,KAAK,CAAC;EAC7B,CAAC;EAED,MAAME,YAAY,GAAIC,IAAY,IAAK;IACrC,OAAOP,QAAQ,CAACQ,QAAQ,KAAKD,IAAI;EACnC,CAAC;EAED,MAAME,QAAQ,GAAG,CACf;IAAEF,IAAI,EAAE,GAAG;IAAEG,KAAK,EAAE;EAAO,CAAC,EAC5B;IAAEH,IAAI,EAAE,QAAQ;IAAEG,KAAK,EAAE;EAAQ,CAAC,EAClC;IAAEH,IAAI,EAAE,QAAQ;IAAEG,KAAK,EAAE;EAAQ,CAAC,CACnC;EAED,oBACEnB,OAAA;IAAQoB,SAAS,EAAC,oEAAoE;IAAAC,QAAA,gBACpFrB,OAAA;MAAKoB,SAAS,EAAC,wCAAwC;MAAAC,QAAA,gBACrDrB,OAAA;QAAKoB,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBAErDrB,OAAA;UAAKoB,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5BrB,OAAA,CAACN,IAAI;YAAC4B,EAAE,EAAC,GAAG;YAACF,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBACxCrB,OAAA;cAAKoB,SAAS,EAAC,oEAAoE;cAAAC,QAAA,eACjFrB,OAAA;gBAAMoB,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,EAAC;cAAC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eACN1B,OAAA;cAAMoB,SAAS,EAAC,2CAA2C;cAAAC,QAAA,EAAC;YAAY;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3E;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGN1B,OAAA;UAAKoB,SAAS,EAAC,0BAA0B;UAAAC,QAAA,EACtCH,QAAQ,CAACS,GAAG,CAAEC,IAAI,iBACjB5B,OAAA,CAACN,IAAI;YAEH4B,EAAE,EAAEM,IAAI,CAACZ,IAAK;YACdI,SAAS,EAAE,2EACTL,YAAY,CAACa,IAAI,CAACZ,IAAI,CAAC,GACnB,gCAAgC,GAChC,iEAAiE,EACpE;YAAAK,QAAA,EAEFO,IAAI,CAACT;UAAK,GARNS,IAAI,CAACZ,IAAI;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OASV,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGN1B,OAAA;UAAKoB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EACnDf,eAAe,gBACdN,OAAA,CAAAE,SAAA;YAAAmB,QAAA,gBACErB,OAAA,CAACN,IAAI;cACH4B,EAAE,EAAC,YAAY;cACfF,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAChC;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAGP1B,OAAA;cAAKoB,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBrB,OAAA;gBACE6B,OAAO,EAAEA,CAAA,KAAMhB,oBAAoB,CAAC,CAACD,iBAAiB,CAAE;gBACxDQ,SAAS,EAAC,iGAAiG;gBAAAC,QAAA,GAE1GhB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEyB,MAAM,gBACX9B,OAAA;kBACE+B,GAAG,EAAE1B,IAAI,CAACyB,MAAO;kBACjBE,GAAG,EAAE3B,IAAI,CAAC4B,QAAS;kBACnBb,SAAS,EAAC;gBAAmC;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC,gBAEF1B,OAAA;kBAAKoB,SAAS,EAAC,sEAAsE;kBAAAC,QAAA,eACnFrB,OAAA;oBAAMoB,SAAS,EAAC,gCAAgC;oBAAAC,QAAA,EAC7ChB,IAAI,GAAGP,WAAW,CAACO,IAAI,CAAC6B,SAAS,EAAE7B,IAAI,CAAC8B,QAAQ,CAAC,GAAG;kBAAG;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CACN,eACD1B,OAAA;kBAAMoB,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,EACrDhB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6B;gBAAS;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,eACP1B,OAAA;kBACEoB,SAAS,EAAE,gEACTR,iBAAiB,GAAG,YAAY,GAAG,EAAE,EACpC;kBACHwB,IAAI,EAAC,MAAM;kBACXC,MAAM,EAAC,cAAc;kBACrBC,OAAO,EAAC,WAAW;kBAAAjB,QAAA,eAEnBrB,OAAA;oBAAMuC,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAACC,CAAC,EAAC;kBAAgB;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,EAGRd,iBAAiB,iBAChBZ,OAAA;gBAAKoB,SAAS,EAAC,gGAAgG;gBAAAC,QAAA,gBAC7GrB,OAAA,CAACN,IAAI;kBACH4B,EAAE,EAAC,UAAU;kBACbF,SAAS,EAAC,kEAAkE;kBAC5ES,OAAO,EAAEA,CAAA,KAAMhB,oBAAoB,CAAC,KAAK,CAAE;kBAAAQ,QAAA,EAC5C;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACP1B,OAAA,CAACN,IAAI;kBACH4B,EAAE,EAAC,kBAAkB;kBACrBF,SAAS,EAAC,kEAAkE;kBAC5ES,OAAO,EAAEA,CAAA,KAAMhB,oBAAoB,CAAC,KAAK,CAAE;kBAAAQ,QAAA,EAC5C;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACP1B,OAAA,CAACN,IAAI;kBACH4B,EAAE,EAAC,mBAAmB;kBACtBF,SAAS,EAAC,kEAAkE;kBAC5ES,OAAO,EAAEA,CAAA,KAAMhB,oBAAoB,CAAC,KAAK,CAAE;kBAAAQ,QAAA,EAC5C;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EACN,CAAArB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsC,IAAI,MAAK,OAAO,iBACrB3C,OAAA,CAACN,IAAI;kBACH4B,EAAE,EAAC,QAAQ;kBACXF,SAAS,EAAC,kEAAkE;kBAC5ES,OAAO,EAAEA,CAAA,KAAMhB,oBAAoB,CAAC,KAAK,CAAE;kBAAAQ,QAAA,EAC5C;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CACP,eACD1B,OAAA;kBAAIoB,SAAS,EAAC;gBAA2B;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC5C1B,OAAA;kBACE6B,OAAO,EAAEf,YAAa;kBACtBM,SAAS,EAAC,uEAAuE;kBAAAC,QAAA,EAClF;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA,eACN,CAAC,gBAEH1B,OAAA;YAAKoB,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CrB,OAAA,CAACN,IAAI;cACH4B,EAAE,EAAC,QAAQ;cACXF,SAAS,EAAC,8FAA8F;cAAAC,QAAA,EACzG;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACP1B,OAAA,CAACN,IAAI;cACH4B,EAAE,EAAC,WAAW;cACdF,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAChC;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGN1B,OAAA;UAAKoB,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxBrB,OAAA;YACE6B,OAAO,EAAEA,CAAA,KAAMlB,aAAa,CAAC,CAACD,UAAU,CAAE;YAC1CU,SAAS,EAAC,+GAA+G;YAAAC,QAAA,eAEzHrB,OAAA;cACEoB,SAAS,EAAC,SAAS;cACnBgB,IAAI,EAAC,MAAM;cACXC,MAAM,EAAC,cAAc;cACrBC,OAAO,EAAC,WAAW;cAAAjB,QAAA,EAElBX,UAAU,gBACTV,OAAA;gBAAMuC,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAAsB;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAE9F1B,OAAA;gBAAMuC,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAAyB;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YACjG;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLhB,UAAU,iBACTV,OAAA;QAAKoB,SAAS,EAAC,8CAA8C;QAAAC,QAAA,eAC3DrB,OAAA;UAAKoB,SAAS,EAAC,WAAW;UAAAC,QAAA,GACvBH,QAAQ,CAACS,GAAG,CAAEC,IAAI,iBACjB5B,OAAA,CAACN,IAAI;YAEH4B,EAAE,EAAEM,IAAI,CAACZ,IAAK;YACdI,SAAS,EAAE,mFACTL,YAAY,CAACa,IAAI,CAACZ,IAAI,CAAC,GACnB,gCAAgC,GAChC,iEAAiE,EACpE;YACHa,OAAO,EAAEA,CAAA,KAAMlB,aAAa,CAAC,KAAK,CAAE;YAAAU,QAAA,EAEnCO,IAAI,CAACT;UAAK,GATNS,IAAI,CAACZ,IAAI;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAUV,CACP,CAAC,EAEDpB,eAAe,gBACdN,OAAA,CAAAE,SAAA;YAAAmB,QAAA,gBACErB,OAAA;cAAIoB,SAAS,EAAC;YAA2B;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5C1B,OAAA,CAACN,IAAI;cACH4B,EAAE,EAAC,YAAY;cACfF,SAAS,EAAC,kHAAkH;cAC5HS,OAAO,EAAEA,CAAA,KAAMlB,aAAa,CAAC,KAAK,CAAE;cAAAU,QAAA,EACrC;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACP1B,OAAA,CAACN,IAAI;cACH4B,EAAE,EAAC,UAAU;cACbF,SAAS,EAAC,kHAAkH;cAC5HS,OAAO,EAAEA,CAAA,KAAMlB,aAAa,CAAC,KAAK,CAAE;cAAAU,QAAA,EACrC;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EACN,CAAArB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsC,IAAI,MAAK,OAAO,iBACrB3C,OAAA,CAACN,IAAI;cACH4B,EAAE,EAAC,QAAQ;cACXF,SAAS,EAAC,kHAAkH;cAC5HS,OAAO,EAAEA,CAAA,KAAMlB,aAAa,CAAC,KAAK,CAAE;cAAAU,QAAA,EACrC;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CACP,eACD1B,OAAA;cACE6B,OAAO,EAAEf,YAAa;cACtBM,SAAS,EAAC,gGAAgG;cAAAC,QAAA,EAC3G;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA,eACT,CAAC,gBAEH1B,OAAA,CAAAE,SAAA;YAAAmB,QAAA,gBACErB,OAAA;cAAIoB,SAAS,EAAC;YAA2B;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5C1B,OAAA,CAACN,IAAI;cACH4B,EAAE,EAAC,QAAQ;cACXF,SAAS,EAAC,kHAAkH;cAC5HS,OAAO,EAAEA,CAAA,KAAMlB,aAAa,CAAC,KAAK,CAAE;cAAAU,QAAA,EACrC;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACP1B,OAAA,CAACN,IAAI;cACH4B,EAAE,EAAC,WAAW;cACdF,SAAS,EAAC,iGAAiG;cAC3GS,OAAO,EAAEA,CAAA,KAAMlB,aAAa,CAAC,KAAK,CAAE;cAAAU,QAAA,EACrC;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,eACP,CACH;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLd,iBAAiB,iBAChBZ,OAAA;MACEoB,SAAS,EAAC,oBAAoB;MAC9BS,OAAO,EAAEA,CAAA,KAAMhB,oBAAoB,CAAC,KAAK;IAAE;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5C,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEb,CAAC;AAACtB,EAAA,CA5QID,MAAgB;EAAA,QACsBN,OAAO,EAChCF,WAAW,EACXC,WAAW;AAAA;AAAAgD,EAAA,GAHxBzC,MAAgB;AA8QtB,eAAeA,MAAM;AAAC,IAAAyC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}