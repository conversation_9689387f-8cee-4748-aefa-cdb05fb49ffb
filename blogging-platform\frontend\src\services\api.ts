import axios, { AxiosInstance, AxiosResponse } from 'axios';
import {
  User,
  Blog,
  Comment,
  LoginCredentials,
  RegisterData,
  AuthResponse,
  BlogFormData,
  CommentFormData,
  ProfileUpdateData,
  PasswordChangeData,
  BlogsResponse,
  CommentsResponse,
  UsersResponse,
  BlogFilters,
  UserFilters,
  PlatformStats,
  ApiError
} from '../types';

class ApiService {
  private api: AxiosInstance;

  constructor() {
    this.api = axios.create({
      baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5000/api',
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor to add auth token
    this.api.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor to handle errors
    this.api.interceptors.response.use(
      (response: AxiosResponse) => response,
      (error) => {
        if (error.response?.status === 401) {
          // Token expired or invalid
          localStorage.removeItem('token');
          localStorage.removeItem('user');
          window.location.href = '/login';
        }
        
        const apiError: ApiError = {
          message: error.response?.data?.message || 'An error occurred',
          errors: error.response?.data?.errors,
          status: error.response?.status,
        };
        
        return Promise.reject(apiError);
      }
    );
  }

  // Auth endpoints
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    const response = await this.api.post<AuthResponse>('/auth/login', credentials);
    return response.data;
  }

  async register(data: RegisterData): Promise<AuthResponse> {
    const response = await this.api.post<AuthResponse>('/auth/register', data);
    return response.data;
  }

  async getProfile(): Promise<{ user: User }> {
    const response = await this.api.get<{ user: User }>('/auth/me');
    return response.data;
  }

  async updateProfile(data: ProfileUpdateData): Promise<{ user: User; message: string }> {
    const response = await this.api.put<{ user: User; message: string }>('/auth/profile', data);
    return response.data;
  }

  async changePassword(data: PasswordChangeData): Promise<{ message: string }> {
    const response = await this.api.post<{ message: string }>('/auth/change-password', data);
    return response.data;
  }

  // Blog endpoints
  async getBlogs(filters?: BlogFilters): Promise<BlogsResponse> {
    const params = new URLSearchParams();
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });
    }
    
    const response = await this.api.get<BlogsResponse>(`/blogs?${params.toString()}`);
    return response.data;
  }

  async getMyBlogs(filters?: { status?: string; search?: string; page?: number; limit?: number }): Promise<BlogsResponse> {
    const params = new URLSearchParams();
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });
    }
    
    const response = await this.api.get<BlogsResponse>(`/blogs/my-blogs?${params.toString()}`);
    return response.data;
  }

  async getBlogBySlug(slug: string): Promise<Blog> {
    const response = await this.api.get<Blog>(`/blogs/${slug}`);
    return response.data;
  }

  async createBlog(data: BlogFormData): Promise<{ blog: Blog; message: string }> {
    const response = await this.api.post<{ blog: Blog; message: string }>('/blogs', data);
    return response.data;
  }

  async updateBlog(id: string, data: Partial<BlogFormData>): Promise<{ blog: Blog; message: string }> {
    const response = await this.api.put<{ blog: Blog; message: string }>(`/blogs/${id}`, data);
    return response.data;
  }

  async deleteBlog(id: string): Promise<{ message: string }> {
    const response = await this.api.delete<{ message: string }>(`/blogs/${id}`);
    return response.data;
  }

  async likeBlog(id: string): Promise<{ message: string; isLiked: boolean; likeCount: number }> {
    const response = await this.api.post<{ message: string; isLiked: boolean; likeCount: number }>(`/blogs/${id}/like`);
    return response.data;
  }

  // Comment endpoints
  async getComments(blogId: string, page?: number, limit?: number): Promise<CommentsResponse> {
    const params = new URLSearchParams();
    if (page) params.append('page', page.toString());
    if (limit) params.append('limit', limit.toString());
    
    const response = await this.api.get<CommentsResponse>(`/comments/blog/${blogId}?${params.toString()}`);
    return response.data;
  }

  async createComment(data: CommentFormData): Promise<{ comment: Comment; message: string }> {
    const response = await this.api.post<{ comment: Comment; message: string }>('/comments', data);
    return response.data;
  }

  async updateComment(id: string, content: string): Promise<{ comment: Comment; message: string }> {
    const response = await this.api.put<{ comment: Comment; message: string }>(`/comments/${id}`, { content });
    return response.data;
  }

  async deleteComment(id: string): Promise<{ message: string }> {
    const response = await this.api.delete<{ message: string }>(`/comments/${id}`);
    return response.data;
  }

  async likeComment(id: string): Promise<{ message: string; isLiked: boolean; likeCount: number }> {
    const response = await this.api.post<{ message: string; isLiked: boolean; likeCount: number }>(`/comments/${id}/like`);
    return response.data;
  }

  async getUserComments(userId: string, page?: number, limit?: number): Promise<CommentsResponse> {
    const params = new URLSearchParams();
    if (page) params.append('page', page.toString());
    if (limit) params.append('limit', limit.toString());
    
    const response = await this.api.get<CommentsResponse>(`/comments/user/${userId}?${params.toString()}`);
    return response.data;
  }

  // User endpoints
  async getUsers(filters?: UserFilters): Promise<UsersResponse> {
    const params = new URLSearchParams();
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });
    }
    
    const response = await this.api.get<UsersResponse>(`/users?${params.toString()}`);
    return response.data;
  }

  async getUserById(id: string): Promise<{ user: User & { stats: { blogCount: number; commentCount: number } } }> {
    const response = await this.api.get<{ user: User & { stats: { blogCount: number; commentCount: number } } }>(`/users/${id}`);
    return response.data;
  }

  async getUserBlogs(userId: string, page?: number, limit?: number): Promise<BlogsResponse & { author: User }> {
    const params = new URLSearchParams();
    if (page) params.append('page', page.toString());
    if (limit) params.append('limit', limit.toString());
    
    const response = await this.api.get<BlogsResponse & { author: User }>(`/users/${userId}/blogs?${params.toString()}`);
    return response.data;
  }

  async updateUserRole(id: string, role: 'user' | 'admin'): Promise<{ message: string; user: Partial<User> }> {
    const response = await this.api.put<{ message: string; user: Partial<User> }>(`/users/${id}/role`, { role });
    return response.data;
  }

  async updateUserStatus(id: string, isActive: boolean): Promise<{ message: string; user: Partial<User> }> {
    const response = await this.api.put<{ message: string; user: Partial<User> }>(`/users/${id}/status`, { isActive });
    return response.data;
  }

  async getPlatformStats(): Promise<PlatformStats> {
    const response = await this.api.get<PlatformStats>('/users/stats/overview');
    return response.data;
  }

  // Health check
  async healthCheck(): Promise<{ status: string; message: string; timestamp: string }> {
    const response = await this.api.get<{ status: string; message: string; timestamp: string }>('/health');
    return response.data;
  }
}

export const apiService = new ApiService();
export default apiService;
