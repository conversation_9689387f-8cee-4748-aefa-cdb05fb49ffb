{"ast": null, "code": "var _jsxFileName = \"D:\\\\D Drive\\\\Projects\\\\Blogging\\\\blogging-platform\\\\frontend\\\\src\\\\pages\\\\CreateBlog.tsx\";\nimport React from 'react';\nimport { Helmet } from 'react-helmet-async';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst CreateBlog = () => {\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Helmet, {\n      children: [/*#__PURE__*/_jsxDEV(\"title\", {\n        children: \"Create Blog - BlogPlatform\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        name: \"description\",\n        content: \"Create and publish your blog post.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-3xl font-bold text-secondary-900 mb-8\",\n        children: \"Create New Blog\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card p-8\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-secondary-600 text-center py-12\",\n          children: \"Blog creation form will be implemented here.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_c = CreateBlog;\nexport default CreateBlog;\nvar _c;\n$RefreshReg$(_c, \"CreateBlog\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CreateBlog", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "content", "className", "_c", "$RefreshReg$"], "sources": ["D:/D Drive/Projects/Blogging/blogging-platform/frontend/src/pages/CreateBlog.tsx"], "sourcesContent": ["import React from 'react';\nimport { Helmet } from 'react-helmet-async';\n\nconst CreateBlog: React.FC = () => {\n  return (\n    <>\n      <Helmet>\n        <title>Create Blog - BlogPlatform</title>\n        <meta name=\"description\" content=\"Create and publish your blog post.\" />\n      </Helmet>\n\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <h1 className=\"text-3xl font-bold text-secondary-900 mb-8\">Create New Blog</h1>\n        \n        <div className=\"card p-8\">\n          <p className=\"text-secondary-600 text-center py-12\">\n            Blog creation form will be implemented here.\n          </p>\n        </div>\n      </div>\n    </>\n  );\n};\n\nexport default CreateBlog;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE5C,MAAMC,UAAoB,GAAGA,CAAA,KAAM;EACjC,oBACEH,OAAA,CAAAE,SAAA;IAAAE,QAAA,gBACEJ,OAAA,CAACF,MAAM;MAAAM,QAAA,gBACLJ,OAAA;QAAAI,QAAA,EAAO;MAA0B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACzCR,OAAA;QAAMS,IAAI,EAAC,aAAa;QAACC,OAAO,EAAC;MAAoC;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClE,CAAC,eAETR,OAAA;MAAKW,SAAS,EAAC,6CAA6C;MAAAP,QAAA,gBAC1DJ,OAAA;QAAIW,SAAS,EAAC,4CAA4C;QAAAP,QAAA,EAAC;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAE/ER,OAAA;QAAKW,SAAS,EAAC,UAAU;QAAAP,QAAA,eACvBJ,OAAA;UAAGW,SAAS,EAAC,sCAAsC;UAAAP,QAAA,EAAC;QAEpD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAACI,EAAA,GAnBIT,UAAoB;AAqB1B,eAAeA,UAAU;AAAC,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}