import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { Blog } from '../types';
import { apiService } from '../services/api';
import { formatDate, getCategoryColor, generateExcerpt } from '../utils/helpers';
import Loading, { BlogCardSkeleton } from '../components/Loading';

const Home: React.FC = () => {
  const [featuredBlogs, setFeaturedBlogs] = useState<Blog[]>([]);
  const [recentBlogs, setRecentBlogs] = useState<Blog[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchBlogs = async () => {
      try {
        setIsLoading(true);
        
        // Fetch recent blogs
        const response = await apiService.getBlogs({ 
          limit: 6, 
          sortBy: 'publishedAt', 
          sortOrder: 'desc' 
        });
        
        setRecentBlogs(response.blogs);
        
        // Use first 3 as featured for now
        setFeaturedBlogs(response.blogs.slice(0, 3));
        
      } catch (err: any) {
        setError(err.message || 'Failed to fetch blogs');
      } finally {
        setIsLoading(false);
      }
    };

    fetchBlogs();
  }, []);

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-secondary-900 mb-2">Something went wrong</h2>
          <p className="text-secondary-600 mb-4">{error}</p>
          <button 
            onClick={() => window.location.reload()} 
            className="btn-primary"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <>
      <Helmet>
        <title>BlogPlatform - Share Your Stories</title>
        <meta name="description" content="A modern blogging platform where writers share their stories and readers discover amazing content." />
        <meta name="keywords" content="blog, writing, stories, articles, content" />
      </Helmet>

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-primary-600 to-primary-800 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Share Your Stories
            </h1>
            <p className="text-xl md:text-2xl text-primary-100 mb-8 max-w-3xl mx-auto">
              A modern blogging platform where writers share their stories and readers discover amazing content.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/register" className="btn-secondary text-lg px-8 py-3">
                Start Writing
              </Link>
              <Link to="/blogs" className="btn-outline text-lg px-8 py-3 border-white text-white hover:bg-white hover:text-primary-600">
                Explore Blogs
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Blogs Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-secondary-900 mb-4">
              Featured Stories
            </h2>
            <p className="text-lg text-secondary-600 max-w-2xl mx-auto">
              Discover the most engaging and popular stories from our community of writers.
            </p>
          </div>

          {isLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {Array.from({ length: 3 }).map((_, i) => (
                <BlogCardSkeleton key={i} />
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {featuredBlogs.map((blog) => (
                <article key={blog._id} className="card overflow-hidden hover:shadow-lg transition-shadow duration-300">
                  {blog.featuredImage && (
                    <div className="aspect-w-16 aspect-h-9">
                      <img
                        src={blog.featuredImage}
                        alt={blog.title}
                        className="w-full h-48 object-cover"
                      />
                    </div>
                  )}
                  <div className="p-6">
                    <div className="flex items-center space-x-2 mb-3">
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getCategoryColor(blog.category)}`}>
                        {blog.category}
                      </span>
                      <span className="text-sm text-secondary-500">
                        {blog.readTime} min read
                      </span>
                    </div>
                    
                    <h3 className="text-xl font-bold text-secondary-900 mb-3 line-clamp-2">
                      <Link to={`/blog/${blog.slug}`} className="hover:text-primary-600 transition-colors duration-200">
                        {blog.title}
                      </Link>
                    </h3>
                    
                    <p className="text-secondary-600 mb-4 line-clamp-3">
                      {blog.excerpt || generateExcerpt(blog.content)}
                    </p>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <Link to={`/user/${blog.author.id}`} className="flex items-center space-x-2 hover:text-primary-600 transition-colors duration-200">
                          {blog.author.avatar ? (
                            <img
                              src={blog.author.avatar}
                              alt={blog.author.fullName}
                              className="w-8 h-8 rounded-full object-cover"
                            />
                          ) : (
                            <div className="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center">
                              <span className="text-white text-sm font-medium">
                                {blog.author.firstName.charAt(0)}{blog.author.lastName.charAt(0)}
                              </span>
                            </div>
                          )}
                          <span className="text-sm font-medium text-secondary-700">
                            {blog.author.fullName}
                          </span>
                        </Link>
                      </div>
                      <span className="text-sm text-secondary-500">
                        {formatDate(blog.publishedAt || blog.createdAt)}
                      </span>
                    </div>
                  </div>
                </article>
              ))}
            </div>
          )}
        </div>
      </section>

      {/* Recent Blogs Section */}
      <section className="py-16 bg-secondary-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center mb-12">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-secondary-900 mb-4">
                Recent Stories
              </h2>
              <p className="text-lg text-secondary-600">
                Fresh content from our community of writers.
              </p>
            </div>
            <Link to="/blogs" className="btn-outline hidden sm:block">
              View All Blogs
            </Link>
          </div>

          {isLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {Array.from({ length: 6 }).map((_, i) => (
                <BlogCardSkeleton key={i} />
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {recentBlogs.map((blog) => (
                <article key={blog._id} className="card overflow-hidden hover:shadow-lg transition-shadow duration-300">
                  <div className="p-6">
                    <div className="flex items-center space-x-2 mb-3">
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getCategoryColor(blog.category)}`}>
                        {blog.category}
                      </span>
                      <span className="text-sm text-secondary-500">
                        {blog.readTime} min read
                      </span>
                    </div>
                    
                    <h3 className="text-xl font-bold text-secondary-900 mb-3 line-clamp-2">
                      <Link to={`/blog/${blog.slug}`} className="hover:text-primary-600 transition-colors duration-200">
                        {blog.title}
                      </Link>
                    </h3>
                    
                    <p className="text-secondary-600 mb-4 line-clamp-3">
                      {blog.excerpt || generateExcerpt(blog.content)}
                    </p>
                    
                    <div className="flex items-center justify-between">
                      <Link to={`/user/${blog.author.id}`} className="flex items-center space-x-2 hover:text-primary-600 transition-colors duration-200">
                        {blog.author.avatar ? (
                          <img
                            src={blog.author.avatar}
                            alt={blog.author.fullName}
                            className="w-8 h-8 rounded-full object-cover"
                          />
                        ) : (
                          <div className="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center">
                            <span className="text-white text-sm font-medium">
                              {blog.author.firstName.charAt(0)}{blog.author.lastName.charAt(0)}
                            </span>
                          </div>
                        )}
                        <span className="text-sm font-medium text-secondary-700">
                          {blog.author.fullName}
                        </span>
                      </Link>
                      <span className="text-sm text-secondary-500">
                        {formatDate(blog.publishedAt || blog.createdAt)}
                      </span>
                    </div>
                  </div>
                </article>
              ))}
            </div>
          )}

          <div className="text-center mt-12 sm:hidden">
            <Link to="/blogs" className="btn-outline">
              View All Blogs
            </Link>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-primary-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Ready to Share Your Story?
          </h2>
          <p className="text-xl text-primary-100 mb-8 max-w-2xl mx-auto">
            Join our community of writers and start sharing your thoughts, experiences, and expertise with the world.
          </p>
          <Link to="/register" className="btn-secondary text-lg px-8 py-3">
            Get Started Today
          </Link>
        </div>
      </section>
    </>
  );
};

export default Home;
