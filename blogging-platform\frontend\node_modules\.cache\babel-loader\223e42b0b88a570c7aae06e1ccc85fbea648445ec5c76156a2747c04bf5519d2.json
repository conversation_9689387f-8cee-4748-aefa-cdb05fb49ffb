{"ast": null, "code": "var _jsxFileName = \"D:\\\\D Drive\\\\Projects\\\\Blogging\\\\blogging-platform\\\\frontend\\\\src\\\\App.tsx\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { HelmetProvider } from 'react-helmet-async';\nimport { AuthProvider } from './context/AuthContext';\nimport Header from './components/Header';\nimport Footer from './components/Footer';\nimport Home from './pages/Home';\nimport BlogList from './pages/BlogList';\nimport BlogDetail from './pages/BlogDetail';\nimport Login from './pages/Login';\nimport Register from './pages/Register';\nimport Dashboard from './pages/Dashboard';\nimport Profile from './pages/Profile';\nimport CreateBlog from './pages/CreateBlog';\nimport EditBlog from './pages/EditBlog';\nimport UserProfile from './pages/UserProfile';\nimport NotFound from './pages/NotFound';\nimport ProtectedRoute from './components/ProtectedRoute';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(HelmetProvider, {\n    children: /*#__PURE__*/_jsxDEV(AuthProvider, {\n      children: /*#__PURE__*/_jsxDEV(Router, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"min-h-screen bg-secondary-50 flex flex-col\",\n          children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n            className: \"flex-1\",\n            children: /*#__PURE__*/_jsxDEV(Routes, {\n              children: [/*#__PURE__*/_jsxDEV(Route, {\n                path: \"/\",\n                element: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 30,\n                  columnNumber: 42\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 30,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/blogs\",\n                element: /*#__PURE__*/_jsxDEV(BlogList, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 31,\n                  columnNumber: 47\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 31,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/blog/:slug\",\n                element: /*#__PURE__*/_jsxDEV(BlogDetail, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 32,\n                  columnNumber: 52\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 32,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/user/:id\",\n                element: /*#__PURE__*/_jsxDEV(UserProfile, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 33,\n                  columnNumber: 50\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 33,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/login\",\n                element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 34,\n                  columnNumber: 47\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 34,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/register\",\n                element: /*#__PURE__*/_jsxDEV(Register, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 35,\n                  columnNumber: 50\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 35,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/dashboard\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  children: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 40,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 39,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 38,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/dashboard/*\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  children: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 45,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 44,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 43,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/profile\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  children: /*#__PURE__*/_jsxDEV(Profile, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 50,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 49,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 48,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/create-blog\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  children: /*#__PURE__*/_jsxDEV(CreateBlog, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 55,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 54,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 53,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/edit-blog/:id\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  children: /*#__PURE__*/_jsxDEV(EditBlog, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 60,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 59,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 58,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"*\",\n                element: /*#__PURE__*/_jsxDEV(NotFound, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 65,\n                  columnNumber: 42\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 65,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 28,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 27,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>th<PERSON><PERSON><PERSON>", "Header", "Footer", "Home", "BlogList", "BlogDetail", "<PERSON><PERSON>", "Register", "Dashboard", "Profile", "CreateBlog", "EditBlog", "UserProfile", "NotFound", "ProtectedRoute", "jsxDEV", "_jsxDEV", "App", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "_c", "$RefreshReg$"], "sources": ["D:/D Drive/Projects/Blogging/blogging-platform/frontend/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { HelmetProvider } from 'react-helmet-async';\nimport { AuthProvider } from './context/AuthContext';\nimport Header from './components/Header';\nimport Footer from './components/Footer';\nimport Home from './pages/Home';\nimport BlogList from './pages/BlogList';\nimport BlogDetail from './pages/BlogDetail';\nimport Login from './pages/Login';\nimport Register from './pages/Register';\nimport Dashboard from './pages/Dashboard';\nimport Profile from './pages/Profile';\nimport CreateBlog from './pages/CreateBlog';\nimport EditBlog from './pages/EditBlog';\nimport UserProfile from './pages/UserProfile';\nimport NotFound from './pages/NotFound';\nimport ProtectedRoute from './components/ProtectedRoute';\n\nfunction App() {\n  return (\n    <HelmetProvider>\n      <AuthProvider>\n        <Router>\n          <div className=\"min-h-screen bg-secondary-50 flex flex-col\">\n            <Header />\n            <main className=\"flex-1\">\n              <Routes>\n                {/* Public Routes */}\n                <Route path=\"/\" element={<Home />} />\n                <Route path=\"/blogs\" element={<BlogList />} />\n                <Route path=\"/blog/:slug\" element={<BlogDetail />} />\n                <Route path=\"/user/:id\" element={<UserProfile />} />\n                <Route path=\"/login\" element={<Login />} />\n                <Route path=\"/register\" element={<Register />} />\n\n                {/* Protected Routes */}\n                <Route path=\"/dashboard\" element={\n                  <ProtectedRoute>\n                    <Dashboard />\n                  </ProtectedRoute>\n                } />\n                <Route path=\"/dashboard/*\" element={\n                  <ProtectedRoute>\n                    <Dashboard />\n                  </ProtectedRoute>\n                } />\n                <Route path=\"/profile\" element={\n                  <ProtectedRoute>\n                    <Profile />\n                  </ProtectedRoute>\n                } />\n                <Route path=\"/create-blog\" element={\n                  <ProtectedRoute>\n                    <CreateBlog />\n                  </ProtectedRoute>\n                } />\n                <Route path=\"/edit-blog/:id\" element={\n                  <ProtectedRoute>\n                    <EditBlog />\n                  </ProtectedRoute>\n                } />\n\n                {/* 404 Route */}\n                <Route path=\"*\" element={<NotFound />} />\n              </Routes>\n            </main>\n            <Footer />\n          </div>\n        </Router>\n      </AuthProvider>\n    </HelmetProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,SAASC,cAAc,QAAQ,oBAAoB;AACnD,SAASC,YAAY,QAAQ,uBAAuB;AACpD,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,cAAc,MAAM,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzD,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACjB,cAAc;IAAAmB,QAAA,eACbF,OAAA,CAAChB,YAAY;MAAAkB,QAAA,eACXF,OAAA,CAACpB,MAAM;QAAAsB,QAAA,eACLF,OAAA;UAAKG,SAAS,EAAC,4CAA4C;UAAAD,QAAA,gBACzDF,OAAA,CAACf,MAAM;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACVP,OAAA;YAAMG,SAAS,EAAC,QAAQ;YAAAD,QAAA,eACtBF,OAAA,CAACnB,MAAM;cAAAqB,QAAA,gBAELF,OAAA,CAAClB,KAAK;gBAAC0B,IAAI,EAAC,GAAG;gBAACC,OAAO,eAAET,OAAA,CAACb,IAAI;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACrCP,OAAA,CAAClB,KAAK;gBAAC0B,IAAI,EAAC,QAAQ;gBAACC,OAAO,eAAET,OAAA,CAACZ,QAAQ;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9CP,OAAA,CAAClB,KAAK;gBAAC0B,IAAI,EAAC,aAAa;gBAACC,OAAO,eAAET,OAAA,CAACX,UAAU;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACrDP,OAAA,CAAClB,KAAK;gBAAC0B,IAAI,EAAC,WAAW;gBAACC,OAAO,eAAET,OAAA,CAACJ,WAAW;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACpDP,OAAA,CAAClB,KAAK;gBAAC0B,IAAI,EAAC,QAAQ;gBAACC,OAAO,eAAET,OAAA,CAACV,KAAK;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC3CP,OAAA,CAAClB,KAAK;gBAAC0B,IAAI,EAAC,WAAW;gBAACC,OAAO,eAAET,OAAA,CAACT,QAAQ;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAGjDP,OAAA,CAAClB,KAAK;gBAAC0B,IAAI,EAAC,YAAY;gBAACC,OAAO,eAC9BT,OAAA,CAACF,cAAc;kBAAAI,QAAA,eACbF,OAAA,CAACR,SAAS;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACJP,OAAA,CAAClB,KAAK;gBAAC0B,IAAI,EAAC,cAAc;gBAACC,OAAO,eAChCT,OAAA,CAACF,cAAc;kBAAAI,QAAA,eACbF,OAAA,CAACR,SAAS;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACJP,OAAA,CAAClB,KAAK;gBAAC0B,IAAI,EAAC,UAAU;gBAACC,OAAO,eAC5BT,OAAA,CAACF,cAAc;kBAAAI,QAAA,eACbF,OAAA,CAACP,OAAO;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACJP,OAAA,CAAClB,KAAK;gBAAC0B,IAAI,EAAC,cAAc;gBAACC,OAAO,eAChCT,OAAA,CAACF,cAAc;kBAAAI,QAAA,eACbF,OAAA,CAACN,UAAU;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACJP,OAAA,CAAClB,KAAK;gBAAC0B,IAAI,EAAC,gBAAgB;gBAACC,OAAO,eAClCT,OAAA,CAACF,cAAc;kBAAAI,QAAA,eACbF,OAAA,CAACL,QAAQ;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAGJP,OAAA,CAAClB,KAAK;gBAAC0B,IAAI,EAAC,GAAG;gBAACC,OAAO,eAAET,OAAA,CAACH,QAAQ;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACPP,OAAA,CAACd,MAAM;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAErB;AAACG,EAAA,GAtDQT,GAAG;AAwDZ,eAAeA,GAAG;AAAC,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}