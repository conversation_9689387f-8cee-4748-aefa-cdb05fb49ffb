{"ast": null, "code": "//\n\nmodule.exports = function shallowEqual(objA, objB, compare, compareContext) {\n  var ret = compare ? compare.call(compareContext, objA, objB) : void 0;\n  if (ret !== void 0) {\n    return !!ret;\n  }\n  if (objA === objB) {\n    return true;\n  }\n  if (typeof objA !== \"object\" || !objA || typeof objB !== \"object\" || !objB) {\n    return false;\n  }\n  var keysA = Object.keys(objA);\n  var keysB = Object.keys(objB);\n  if (keysA.length !== keysB.length) {\n    return false;\n  }\n  var bHasOwnProperty = Object.prototype.hasOwnProperty.bind(objB);\n\n  // Test for A's keys different from B.\n  for (var idx = 0; idx < keysA.length; idx++) {\n    var key = keysA[idx];\n    if (!bHasOwnProperty(key)) {\n      return false;\n    }\n    var valueA = objA[key];\n    var valueB = objB[key];\n    ret = compare ? compare.call(compareContext, valueA, valueB, key) : void 0;\n    if (ret === false || ret === void 0 && valueA !== valueB) {\n      return false;\n    }\n  }\n  return true;\n};", "map": {"version": 3, "names": ["module", "exports", "shallowEqual", "objA", "objB", "compare", "compareContext", "ret", "call", "keysA", "Object", "keys", "keysB", "length", "bHasOwnProperty", "prototype", "hasOwnProperty", "bind", "idx", "key", "valueA", "valueB"], "sources": ["D:/D Drive/Projects/Blogging/blogging-platform/frontend/node_modules/shallowequal/index.js"], "sourcesContent": ["//\n\nmodule.exports = function shallowEqual(objA, objB, compare, compareContext) {\n  var ret = compare ? compare.call(compareContext, objA, objB) : void 0;\n\n  if (ret !== void 0) {\n    return !!ret;\n  }\n\n  if (objA === objB) {\n    return true;\n  }\n\n  if (typeof objA !== \"object\" || !objA || typeof objB !== \"object\" || !objB) {\n    return false;\n  }\n\n  var keysA = Object.keys(objA);\n  var keysB = Object.keys(objB);\n\n  if (keysA.length !== keysB.length) {\n    return false;\n  }\n\n  var bHasOwnProperty = Object.prototype.hasOwnProperty.bind(objB);\n\n  // Test for A's keys different from B.\n  for (var idx = 0; idx < keysA.length; idx++) {\n    var key = keysA[idx];\n\n    if (!bHasOwnProperty(key)) {\n      return false;\n    }\n\n    var valueA = objA[key];\n    var valueB = objB[key];\n\n    ret = compare ? compare.call(compareContext, valueA, valueB, key) : void 0;\n\n    if (ret === false || (ret === void 0 && valueA !== valueB)) {\n      return false;\n    }\n  }\n\n  return true;\n};\n"], "mappings": "AAAA;;AAEAA,MAAM,CAACC,OAAO,GAAG,SAASC,YAAYA,CAACC,IAAI,EAAEC,IAAI,EAAEC,OAAO,EAAEC,cAAc,EAAE;EAC1E,IAAIC,GAAG,GAAGF,OAAO,GAAGA,OAAO,CAACG,IAAI,CAACF,cAAc,EAAEH,IAAI,EAAEC,IAAI,CAAC,GAAG,KAAK,CAAC;EAErE,IAAIG,GAAG,KAAK,KAAK,CAAC,EAAE;IAClB,OAAO,CAAC,CAACA,GAAG;EACd;EAEA,IAAIJ,IAAI,KAAKC,IAAI,EAAE;IACjB,OAAO,IAAI;EACb;EAEA,IAAI,OAAOD,IAAI,KAAK,QAAQ,IAAI,CAACA,IAAI,IAAI,OAAOC,IAAI,KAAK,QAAQ,IAAI,CAACA,IAAI,EAAE;IAC1E,OAAO,KAAK;EACd;EAEA,IAAIK,KAAK,GAAGC,MAAM,CAACC,IAAI,CAACR,IAAI,CAAC;EAC7B,IAAIS,KAAK,GAAGF,MAAM,CAACC,IAAI,CAACP,IAAI,CAAC;EAE7B,IAAIK,KAAK,CAACI,MAAM,KAAKD,KAAK,CAACC,MAAM,EAAE;IACjC,OAAO,KAAK;EACd;EAEA,IAAIC,eAAe,GAAGJ,MAAM,CAACK,SAAS,CAACC,cAAc,CAACC,IAAI,CAACb,IAAI,CAAC;;EAEhE;EACA,KAAK,IAAIc,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGT,KAAK,CAACI,MAAM,EAAEK,GAAG,EAAE,EAAE;IAC3C,IAAIC,GAAG,GAAGV,KAAK,CAACS,GAAG,CAAC;IAEpB,IAAI,CAACJ,eAAe,CAACK,GAAG,CAAC,EAAE;MACzB,OAAO,KAAK;IACd;IAEA,IAAIC,MAAM,GAAGjB,IAAI,CAACgB,GAAG,CAAC;IACtB,IAAIE,MAAM,GAAGjB,IAAI,CAACe,GAAG,CAAC;IAEtBZ,GAAG,GAAGF,OAAO,GAAGA,OAAO,CAACG,IAAI,CAACF,cAAc,EAAEc,MAAM,EAAEC,MAAM,EAAEF,GAAG,CAAC,GAAG,KAAK,CAAC;IAE1E,IAAIZ,GAAG,KAAK,KAAK,IAAKA,GAAG,KAAK,KAAK,CAAC,IAAIa,MAAM,KAAKC,MAAO,EAAE;MAC1D,OAAO,KAAK;IACd;EACF;EAEA,OAAO,IAAI;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}