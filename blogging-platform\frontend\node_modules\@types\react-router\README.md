# Installation
> `npm install --save @types/react-router`

# Summary
This package contains type definitions for React Router (https://github.com/ReactTraining/react-router).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-router.

### Additional Details
 * Last updated: Fri, 23 Dec 2022 11:02:45 GMT
 * Dependencies: [@types/history](https://npmjs.com/package/@types/history), [@types/react](https://npmjs.com/package/@types/react)
 * Global values: none

# Credits
These definitions were written by [<PERSON>](https://github.com/sergey-butur<PERSON><PERSON>), [<PERSON><PERSON>](https://github.com/mrk21), [<PERSON><PERSON><PERSON><PERSON>](https://github.com/vasek17), [<PERSON>](https://github.com/ngbrown), [<PERSON>](https://github.com/awendland), [<PERSON><PERSON><PERSON>](https://github.com/<PERSON><PERSON><PERSON>), [<PERSON>](https://github.com/johnny<PERSON>illy), [<PERSON><PERSON>](https://github.com/<PERSON><PERSON>ay), [Dovydas <PERSON>kas](https://github.com/DovydasNavickas), [Huy Nguyen](https://github.com/huy-nguyen), [Jérémy Fauvel](https://github.com/grmiade), [<PERSON> <PERSON>](https://github.com/DaIgeb), [Egor Shulga](https://github.com/egorshulga), [Rahul Raina](https://github.com/rraina), [Duong Tran](https://github.com/t49tran), [Ben Smith](https://github.com/8enSmith), [Wesley Tsai](https://github.com/wezleytsai), [Sebastian Silbermann](https://github.com/eps1lon), [Nicholas Hehr](https://github.com/HipsterBrown), and [Pawel Fajfer](https://github.com/pawfa).
