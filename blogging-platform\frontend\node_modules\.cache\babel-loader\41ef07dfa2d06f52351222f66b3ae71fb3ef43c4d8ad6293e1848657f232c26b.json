{"ast": null, "code": "var _jsxFileName = \"D:\\\\D Drive\\\\Projects\\\\Blogging\\\\blogging-platform\\\\frontend\\\\src\\\\pages\\\\EditBlog.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useParams } from 'react-router-dom';\nimport { Helmet } from 'react-helmet-async';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst EditBlog = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Helmet, {\n      children: [/*#__PURE__*/_jsxDEV(\"title\", {\n        children: \"Edit Blog - BlogPlatform\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        name: \"description\",\n        content: \"Edit your blog post.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-3xl font-bold text-secondary-900 mb-8\",\n        children: \"Edit Blog\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card p-8\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-secondary-600 text-center py-12\",\n          children: [\"Blog editing form will be implemented here for blog ID: \", id]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(EditBlog, \"yQgCIz/jJfqV1l9s2yoba81MT5A=\", false, function () {\n  return [useParams];\n});\n_c = EditBlog;\nexport default EditBlog;\nvar _c;\n$RefreshReg$(_c, \"EditBlog\");", "map": {"version": 3, "names": ["React", "useParams", "<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "EditBlog", "_s", "id", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "content", "className", "_c", "$RefreshReg$"], "sources": ["D:/D Drive/Projects/Blogging/blogging-platform/frontend/src/pages/EditBlog.tsx"], "sourcesContent": ["import React from 'react';\nimport { useParams } from 'react-router-dom';\nimport { Helmet } from 'react-helmet-async';\n\nconst EditBlog: React.FC = () => {\n  const { id } = useParams<{ id: string }>();\n\n  return (\n    <>\n      <Helmet>\n        <title>Edit Blog - BlogPlatform</title>\n        <meta name=\"description\" content=\"Edit your blog post.\" />\n      </Helmet>\n\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <h1 className=\"text-3xl font-bold text-secondary-900 mb-8\">Edit Blog</h1>\n        \n        <div className=\"card p-8\">\n          <p className=\"text-secondary-600 text-center py-12\">\n            Blog editing form will be implemented here for blog ID: {id}\n          </p>\n        </div>\n      </div>\n    </>\n  );\n};\n\nexport default EditBlog;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,MAAM,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE5C,MAAMC,QAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM;IAAEC;EAAG,CAAC,GAAGR,SAAS,CAAiB,CAAC;EAE1C,oBACEG,OAAA,CAAAE,SAAA;IAAAI,QAAA,gBACEN,OAAA,CAACF,MAAM;MAAAQ,QAAA,gBACLN,OAAA;QAAAM,QAAA,EAAO;MAAwB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACvCV,OAAA;QAAMW,IAAI,EAAC,aAAa;QAACC,OAAO,EAAC;MAAsB;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpD,CAAC,eAETV,OAAA;MAAKa,SAAS,EAAC,6CAA6C;MAAAP,QAAA,gBAC1DN,OAAA;QAAIa,SAAS,EAAC,4CAA4C;QAAAP,QAAA,EAAC;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAEzEV,OAAA;QAAKa,SAAS,EAAC,UAAU;QAAAP,QAAA,eACvBN,OAAA;UAAGa,SAAS,EAAC,sCAAsC;UAAAP,QAAA,GAAC,0DACM,EAACD,EAAE;QAAA;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAACN,EAAA,CArBID,QAAkB;EAAA,QACPN,SAAS;AAAA;AAAAiB,EAAA,GADpBX,QAAkB;AAuBxB,eAAeA,QAAQ;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}