{"ast": null, "code": "/* global __webpack_require__ */\nvar Refresh = require('react-refresh/runtime');\n\n/**\n * Extracts exports from a webpack module object.\n * @param {string} moduleId A Webpack module ID.\n * @returns {*} An exports object from the module.\n */\nfunction getModuleExports(moduleId) {\n  if (typeof moduleId === 'undefined') {\n    // `moduleId` is unavailable, which indicates that this module is not in the cache,\n    // which means we won't be able to capture any exports,\n    // and thus they cannot be refreshed safely.\n    // These are likely runtime or dynamically generated modules.\n    return {};\n  }\n  var maybeModule = __webpack_require__.c[moduleId];\n  if (typeof maybeModule === 'undefined') {\n    // `moduleId` is available but the module in cache is unavailable,\n    // which indicates the module is somehow corrupted (e.g. broken Webpacak `module` globals).\n    // We will warn the user (as this is likely a mistake) and assume they cannot be refreshed.\n    console.warn('[React Refresh] Failed to get exports for module: ' + moduleId + '.');\n    return {};\n  }\n  var exportsOrPromise = maybeModule.exports;\n  if (typeof Promise !== 'undefined' && exportsOrPromise instanceof Promise) {\n    return exportsOrPromise.then(function (exports) {\n      return exports;\n    });\n  }\n  return exportsOrPromise;\n}\n\n/**\n * Calculates the signature of a React refresh boundary.\n * If this signature changes, it's unsafe to accept the boundary.\n *\n * This implementation is based on the one in [Metro](https://github.com/facebook/metro/blob/907d6af22ac6ebe58572be418e9253a90665ecbd/packages/metro/src/lib/polyfills/require.js#L795-L816).\n * @param {*} moduleExports A Webpack module exports object.\n * @returns {string[]} A React refresh boundary signature array.\n */\nfunction getReactRefreshBoundarySignature(moduleExports) {\n  var signature = [];\n  signature.push(Refresh.getFamilyByType(moduleExports));\n  if (moduleExports == null || typeof moduleExports !== 'object') {\n    // Exit if we can't iterate over exports.\n    return signature;\n  }\n  for (var key in moduleExports) {\n    if (key === '__esModule') {\n      continue;\n    }\n    signature.push(key);\n    signature.push(Refresh.getFamilyByType(moduleExports[key]));\n  }\n  return signature;\n}\n\n/**\n * Creates a data object to be retained across refreshes.\n * This object should not transtively reference previous exports,\n * which can form infinite chain of objects across refreshes, which can pressure RAM.\n *\n * @param {*} moduleExports A Webpack module exports object.\n * @returns {*} A React refresh boundary signature array.\n */\nfunction getWebpackHotData(moduleExports) {\n  return {\n    signature: getReactRefreshBoundarySignature(moduleExports),\n    isReactRefreshBoundary: isReactRefreshBoundary(moduleExports)\n  };\n}\n\n/**\n * Creates a helper that performs a delayed React refresh.\n * @returns {function(function(): void): void} A debounced React refresh function.\n */\nfunction createDebounceUpdate() {\n  /**\n   * A cached setTimeout handler.\n   * @type {number | undefined}\n   */\n  var refreshTimeout;\n\n  /**\n   * Performs react refresh on a delay and clears the error overlay.\n   * @param {function(): void} callback\n   * @returns {void}\n   */\n  function enqueueUpdate(callback) {\n    if (typeof refreshTimeout === 'undefined') {\n      refreshTimeout = setTimeout(function () {\n        refreshTimeout = undefined;\n        Refresh.performReactRefresh();\n        callback();\n      }, 30);\n    }\n  }\n  return enqueueUpdate;\n}\n\n/**\n * Checks if all exports are likely a React component.\n *\n * This implementation is based on the one in [Metro](https://github.com/facebook/metro/blob/febdba2383113c88296c61e28e4ef6a7f4939fda/packages/metro/src/lib/polyfills/require.js#L748-L774).\n * @param {*} moduleExports A Webpack module exports object.\n * @returns {boolean} Whether the exports are React component like.\n */\nfunction isReactRefreshBoundary(moduleExports) {\n  if (Refresh.isLikelyComponentType(moduleExports)) {\n    return true;\n  }\n  if (moduleExports === undefined || moduleExports === null || typeof moduleExports !== 'object') {\n    // Exit if we can't iterate over exports.\n    return false;\n  }\n  var hasExports = false;\n  var areAllExportsComponents = true;\n  for (var key in moduleExports) {\n    hasExports = true;\n\n    // This is the ES Module indicator flag\n    if (key === '__esModule') {\n      continue;\n    }\n\n    // We can (and have to) safely execute getters here,\n    // as Webpack manually assigns harmony exports to getters,\n    // without any side-effects attached.\n    // Ref: https://github.com/webpack/webpack/blob/b93048643fe74de2a6931755911da1212df55897/lib/MainTemplate.js#L281\n    var exportValue = moduleExports[key];\n    if (!Refresh.isLikelyComponentType(exportValue)) {\n      areAllExportsComponents = false;\n    }\n  }\n  return hasExports && areAllExportsComponents;\n}\n\n/**\n * Checks if exports are likely a React component and registers them.\n *\n * This implementation is based on the one in [Metro](https://github.com/facebook/metro/blob/febdba2383113c88296c61e28e4ef6a7f4939fda/packages/metro/src/lib/polyfills/require.js#L818-L835).\n * @param {*} moduleExports A Webpack module exports object.\n * @param {string} moduleId A Webpack module ID.\n * @returns {void}\n */\nfunction registerExportsForReactRefresh(moduleExports, moduleId) {\n  if (Refresh.isLikelyComponentType(moduleExports)) {\n    // Register module.exports if it is likely a component\n    Refresh.register(moduleExports, moduleId + ' %exports%');\n  }\n  if (moduleExports === undefined || moduleExports === null || typeof moduleExports !== 'object') {\n    // Exit if we can't iterate over the exports.\n    return;\n  }\n  for (var key in moduleExports) {\n    // Skip registering the ES Module indicator\n    if (key === '__esModule') {\n      continue;\n    }\n    var exportValue = moduleExports[key];\n    if (Refresh.isLikelyComponentType(exportValue)) {\n      var typeID = moduleId + ' %exports% ' + key;\n      Refresh.register(exportValue, typeID);\n    }\n  }\n}\n\n/**\n * Compares previous and next module objects to check for mutated boundaries.\n *\n * This implementation is based on the one in [Metro](https://github.com/facebook/metro/blob/907d6af22ac6ebe58572be418e9253a90665ecbd/packages/metro/src/lib/polyfills/require.js#L776-L792).\n * @param {*} prevSignature The signature of the current Webpack module exports object.\n * @param {*} nextSignature The signature of the next Webpack module exports object.\n * @returns {boolean} Whether the React refresh boundary should be invalidated.\n */\nfunction shouldInvalidateReactRefreshBoundary(prevSignature, nextSignature) {\n  if (prevSignature.length !== nextSignature.length) {\n    return true;\n  }\n  for (var i = 0; i < nextSignature.length; i += 1) {\n    if (prevSignature[i] !== nextSignature[i]) {\n      return true;\n    }\n  }\n  return false;\n}\nvar enqueueUpdate = createDebounceUpdate();\nfunction executeRuntime(moduleExports, moduleId, webpackHot, refreshOverlay, isTest) {\n  registerExportsForReactRefresh(moduleExports, moduleId);\n  if (webpackHot) {\n    var isHotUpdate = !!webpackHot.data;\n    var prevData;\n    if (isHotUpdate) {\n      prevData = webpackHot.data.prevData;\n    }\n    if (isReactRefreshBoundary(moduleExports)) {\n      webpackHot.dispose(\n      /**\n       * A callback to performs a full refresh if React has unrecoverable errors,\n       * and also caches the to-be-disposed module.\n       * @param {*} data A hot module data object from Webpack HMR.\n       * @returns {void}\n       */\n      function hotDisposeCallback(data) {\n        // We have to mutate the data object to get data registered and cached\n        data.prevData = getWebpackHotData(moduleExports);\n      });\n      webpackHot.accept(\n      /**\n       * An error handler to allow self-recovering behaviours.\n       * @param {Error} error An error occurred during evaluation of a module.\n       * @returns {void}\n       */\n      function hotErrorHandler(error) {\n        if (typeof refreshOverlay !== 'undefined' && refreshOverlay) {\n          refreshOverlay.handleRuntimeError(error);\n        }\n        if (typeof isTest !== 'undefined' && isTest) {\n          if (window.onHotAcceptError) {\n            window.onHotAcceptError(error.message);\n          }\n        }\n        __webpack_require__.c[moduleId].hot.accept(hotErrorHandler);\n      });\n      if (isHotUpdate) {\n        if (prevData && prevData.isReactRefreshBoundary && shouldInvalidateReactRefreshBoundary(prevData.signature, getReactRefreshBoundarySignature(moduleExports))) {\n          webpackHot.invalidate();\n        } else {\n          enqueueUpdate(\n          /**\n           * A function to dismiss the error overlay after performing React refresh.\n           * @returns {void}\n           */\n          function updateCallback() {\n            if (typeof refreshOverlay !== 'undefined' && refreshOverlay) {\n              refreshOverlay.clearRuntimeErrors();\n            }\n          });\n        }\n      }\n    } else {\n      if (isHotUpdate && typeof prevData !== 'undefined') {\n        webpackHot.invalidate();\n      }\n    }\n  }\n}\nmodule.exports = Object.freeze({\n  enqueueUpdate: enqueueUpdate,\n  executeRuntime: executeRuntime,\n  getModuleExports: getModuleExports,\n  isReactRefreshBoundary: isReactRefreshBoundary,\n  registerExportsForReactRefresh: registerExportsForReactRefresh\n});", "map": {"version": 3, "names": ["Refresh", "require", "getModuleExports", "moduleId", "maybeModule", "__webpack_require__", "c", "console", "warn", "exportsOrPromise", "exports", "Promise", "then", "getReactRefreshBoundarySignature", "moduleExports", "signature", "push", "getFamilyByType", "key", "getWebpackHotData", "isReactRefreshBoundary", "createDebounceUpdate", "refreshTimeout", "enqueueUpdate", "callback", "setTimeout", "undefined", "performReactRefresh", "isLikelyComponentType", "hasExports", "areAllExportsComponents", "exportValue", "registerExportsForReactRefresh", "register", "typeID", "shouldInvalidateReactRefreshBoundary", "prevSignature", "nextSignature", "length", "i", "executeRuntime", "webpackHot", "refreshOverlay", "isTest", "isHotUpdate", "data", "prevData", "dispose", "hotDisposeCallback", "accept", "hotErrorHandler", "error", "handleRuntimeError", "window", "onHotAcceptError", "message", "hot", "invalidate", "updateCallback", "clearRuntimeErrors", "module", "Object", "freeze"], "sources": ["D:/D Drive/Projects/Blogging/blogging-platform/frontend/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js"], "sourcesContent": ["/* global __webpack_require__ */\nvar Refresh = require('react-refresh/runtime');\n\n/**\n * Extracts exports from a webpack module object.\n * @param {string} moduleId A Webpack module ID.\n * @returns {*} An exports object from the module.\n */\nfunction getModuleExports(moduleId) {\n  if (typeof moduleId === 'undefined') {\n    // `moduleId` is unavailable, which indicates that this module is not in the cache,\n    // which means we won't be able to capture any exports,\n    // and thus they cannot be refreshed safely.\n    // These are likely runtime or dynamically generated modules.\n    return {};\n  }\n\n  var maybeModule = __webpack_require__.c[moduleId];\n  if (typeof maybeModule === 'undefined') {\n    // `moduleId` is available but the module in cache is unavailable,\n    // which indicates the module is somehow corrupted (e.g. broken Webpacak `module` globals).\n    // We will warn the user (as this is likely a mistake) and assume they cannot be refreshed.\n    console.warn('[React Refresh] Failed to get exports for module: ' + moduleId + '.');\n    return {};\n  }\n\n  var exportsOrPromise = maybeModule.exports;\n  if (typeof Promise !== 'undefined' && exportsOrPromise instanceof Promise) {\n    return exportsOrPromise.then(function (exports) {\n      return exports;\n    });\n  }\n  return exportsOrPromise;\n}\n\n/**\n * Calculates the signature of a React refresh boundary.\n * If this signature changes, it's unsafe to accept the boundary.\n *\n * This implementation is based on the one in [Metro](https://github.com/facebook/metro/blob/907d6af22ac6ebe58572be418e9253a90665ecbd/packages/metro/src/lib/polyfills/require.js#L795-L816).\n * @param {*} moduleExports A Webpack module exports object.\n * @returns {string[]} A React refresh boundary signature array.\n */\nfunction getReactRefreshBoundarySignature(moduleExports) {\n  var signature = [];\n  signature.push(Refresh.getFamilyByType(moduleExports));\n\n  if (moduleExports == null || typeof moduleExports !== 'object') {\n    // Exit if we can't iterate over exports.\n    return signature;\n  }\n\n  for (var key in moduleExports) {\n    if (key === '__esModule') {\n      continue;\n    }\n\n    signature.push(key);\n    signature.push(Refresh.getFamilyByType(moduleExports[key]));\n  }\n\n  return signature;\n}\n\n/**\n * Creates a data object to be retained across refreshes.\n * This object should not transtively reference previous exports,\n * which can form infinite chain of objects across refreshes, which can pressure RAM.\n *\n * @param {*} moduleExports A Webpack module exports object.\n * @returns {*} A React refresh boundary signature array.\n */\nfunction getWebpackHotData(moduleExports) {\n  return {\n    signature: getReactRefreshBoundarySignature(moduleExports),\n    isReactRefreshBoundary: isReactRefreshBoundary(moduleExports),\n  };\n}\n\n/**\n * Creates a helper that performs a delayed React refresh.\n * @returns {function(function(): void): void} A debounced React refresh function.\n */\nfunction createDebounceUpdate() {\n  /**\n   * A cached setTimeout handler.\n   * @type {number | undefined}\n   */\n  var refreshTimeout;\n\n  /**\n   * Performs react refresh on a delay and clears the error overlay.\n   * @param {function(): void} callback\n   * @returns {void}\n   */\n  function enqueueUpdate(callback) {\n    if (typeof refreshTimeout === 'undefined') {\n      refreshTimeout = setTimeout(function () {\n        refreshTimeout = undefined;\n        Refresh.performReactRefresh();\n        callback();\n      }, 30);\n    }\n  }\n\n  return enqueueUpdate;\n}\n\n/**\n * Checks if all exports are likely a React component.\n *\n * This implementation is based on the one in [Metro](https://github.com/facebook/metro/blob/febdba2383113c88296c61e28e4ef6a7f4939fda/packages/metro/src/lib/polyfills/require.js#L748-L774).\n * @param {*} moduleExports A Webpack module exports object.\n * @returns {boolean} Whether the exports are React component like.\n */\nfunction isReactRefreshBoundary(moduleExports) {\n  if (Refresh.isLikelyComponentType(moduleExports)) {\n    return true;\n  }\n  if (moduleExports === undefined || moduleExports === null || typeof moduleExports !== 'object') {\n    // Exit if we can't iterate over exports.\n    return false;\n  }\n\n  var hasExports = false;\n  var areAllExportsComponents = true;\n  for (var key in moduleExports) {\n    hasExports = true;\n\n    // This is the ES Module indicator flag\n    if (key === '__esModule') {\n      continue;\n    }\n\n    // We can (and have to) safely execute getters here,\n    // as Webpack manually assigns harmony exports to getters,\n    // without any side-effects attached.\n    // Ref: https://github.com/webpack/webpack/blob/b93048643fe74de2a6931755911da1212df55897/lib/MainTemplate.js#L281\n    var exportValue = moduleExports[key];\n    if (!Refresh.isLikelyComponentType(exportValue)) {\n      areAllExportsComponents = false;\n    }\n  }\n\n  return hasExports && areAllExportsComponents;\n}\n\n/**\n * Checks if exports are likely a React component and registers them.\n *\n * This implementation is based on the one in [Metro](https://github.com/facebook/metro/blob/febdba2383113c88296c61e28e4ef6a7f4939fda/packages/metro/src/lib/polyfills/require.js#L818-L835).\n * @param {*} moduleExports A Webpack module exports object.\n * @param {string} moduleId A Webpack module ID.\n * @returns {void}\n */\nfunction registerExportsForReactRefresh(moduleExports, moduleId) {\n  if (Refresh.isLikelyComponentType(moduleExports)) {\n    // Register module.exports if it is likely a component\n    Refresh.register(moduleExports, moduleId + ' %exports%');\n  }\n\n  if (moduleExports === undefined || moduleExports === null || typeof moduleExports !== 'object') {\n    // Exit if we can't iterate over the exports.\n    return;\n  }\n\n  for (var key in moduleExports) {\n    // Skip registering the ES Module indicator\n    if (key === '__esModule') {\n      continue;\n    }\n\n    var exportValue = moduleExports[key];\n    if (Refresh.isLikelyComponentType(exportValue)) {\n      var typeID = moduleId + ' %exports% ' + key;\n      Refresh.register(exportValue, typeID);\n    }\n  }\n}\n\n/**\n * Compares previous and next module objects to check for mutated boundaries.\n *\n * This implementation is based on the one in [Metro](https://github.com/facebook/metro/blob/907d6af22ac6ebe58572be418e9253a90665ecbd/packages/metro/src/lib/polyfills/require.js#L776-L792).\n * @param {*} prevSignature The signature of the current Webpack module exports object.\n * @param {*} nextSignature The signature of the next Webpack module exports object.\n * @returns {boolean} Whether the React refresh boundary should be invalidated.\n */\nfunction shouldInvalidateReactRefreshBoundary(prevSignature, nextSignature) {\n  if (prevSignature.length !== nextSignature.length) {\n    return true;\n  }\n\n  for (var i = 0; i < nextSignature.length; i += 1) {\n    if (prevSignature[i] !== nextSignature[i]) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nvar enqueueUpdate = createDebounceUpdate();\nfunction executeRuntime(moduleExports, moduleId, webpackHot, refreshOverlay, isTest) {\n  registerExportsForReactRefresh(moduleExports, moduleId);\n\n  if (webpackHot) {\n    var isHotUpdate = !!webpackHot.data;\n    var prevData;\n    if (isHotUpdate) {\n      prevData = webpackHot.data.prevData;\n    }\n\n    if (isReactRefreshBoundary(moduleExports)) {\n      webpackHot.dispose(\n        /**\n         * A callback to performs a full refresh if React has unrecoverable errors,\n         * and also caches the to-be-disposed module.\n         * @param {*} data A hot module data object from Webpack HMR.\n         * @returns {void}\n         */\n        function hotDisposeCallback(data) {\n          // We have to mutate the data object to get data registered and cached\n          data.prevData = getWebpackHotData(moduleExports);\n        }\n      );\n      webpackHot.accept(\n        /**\n         * An error handler to allow self-recovering behaviours.\n         * @param {Error} error An error occurred during evaluation of a module.\n         * @returns {void}\n         */\n        function hotErrorHandler(error) {\n          if (typeof refreshOverlay !== 'undefined' && refreshOverlay) {\n            refreshOverlay.handleRuntimeError(error);\n          }\n\n          if (typeof isTest !== 'undefined' && isTest) {\n            if (window.onHotAcceptError) {\n              window.onHotAcceptError(error.message);\n            }\n          }\n\n          __webpack_require__.c[moduleId].hot.accept(hotErrorHandler);\n        }\n      );\n\n      if (isHotUpdate) {\n        if (\n          prevData &&\n          prevData.isReactRefreshBoundary &&\n          shouldInvalidateReactRefreshBoundary(\n            prevData.signature,\n            getReactRefreshBoundarySignature(moduleExports)\n          )\n        ) {\n          webpackHot.invalidate();\n        } else {\n          enqueueUpdate(\n            /**\n             * A function to dismiss the error overlay after performing React refresh.\n             * @returns {void}\n             */\n            function updateCallback() {\n              if (typeof refreshOverlay !== 'undefined' && refreshOverlay) {\n                refreshOverlay.clearRuntimeErrors();\n              }\n            }\n          );\n        }\n      }\n    } else {\n      if (isHotUpdate && typeof prevData !== 'undefined') {\n        webpackHot.invalidate();\n      }\n    }\n  }\n}\n\nmodule.exports = Object.freeze({\n  enqueueUpdate: enqueueUpdate,\n  executeRuntime: executeRuntime,\n  getModuleExports: getModuleExports,\n  isReactRefreshBoundary: isReactRefreshBoundary,\n  registerExportsForReactRefresh: registerExportsForReactRefresh,\n});\n"], "mappings": "AAAA;AACA,IAAIA,OAAO,GAAGC,OAAO,CAAC,uBAAuB,CAAC;;AAE9C;AACA;AACA;AACA;AACA;AACA,SAASC,gBAAgBA,CAACC,QAAQ,EAAE;EAClC,IAAI,OAAOA,QAAQ,KAAK,WAAW,EAAE;IACnC;IACA;IACA;IACA;IACA,OAAO,CAAC,CAAC;EACX;EAEA,IAAIC,WAAW,GAAGC,mBAAmB,CAACC,CAAC,CAACH,QAAQ,CAAC;EACjD,IAAI,OAAOC,WAAW,KAAK,WAAW,EAAE;IACtC;IACA;IACA;IACAG,OAAO,CAACC,IAAI,CAAC,oDAAoD,GAAGL,QAAQ,GAAG,GAAG,CAAC;IACnF,OAAO,CAAC,CAAC;EACX;EAEA,IAAIM,gBAAgB,GAAGL,WAAW,CAACM,OAAO;EAC1C,IAAI,OAAOC,OAAO,KAAK,WAAW,IAAIF,gBAAgB,YAAYE,OAAO,EAAE;IACzE,OAAOF,gBAAgB,CAACG,IAAI,CAAC,UAAUF,OAAO,EAAE;MAC9C,OAAOA,OAAO;IAChB,CAAC,CAAC;EACJ;EACA,OAAOD,gBAAgB;AACzB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,gCAAgCA,CAACC,aAAa,EAAE;EACvD,IAAIC,SAAS,GAAG,EAAE;EAClBA,SAAS,CAACC,IAAI,CAAChB,OAAO,CAACiB,eAAe,CAACH,aAAa,CAAC,CAAC;EAEtD,IAAIA,aAAa,IAAI,IAAI,IAAI,OAAOA,aAAa,KAAK,QAAQ,EAAE;IAC9D;IACA,OAAOC,SAAS;EAClB;EAEA,KAAK,IAAIG,GAAG,IAAIJ,aAAa,EAAE;IAC7B,IAAII,GAAG,KAAK,YAAY,EAAE;MACxB;IACF;IAEAH,SAAS,CAACC,IAAI,CAACE,GAAG,CAAC;IACnBH,SAAS,CAACC,IAAI,CAAChB,OAAO,CAACiB,eAAe,CAACH,aAAa,CAACI,GAAG,CAAC,CAAC,CAAC;EAC7D;EAEA,OAAOH,SAAS;AAClB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,iBAAiBA,CAACL,aAAa,EAAE;EACxC,OAAO;IACLC,SAAS,EAAEF,gCAAgC,CAACC,aAAa,CAAC;IAC1DM,sBAAsB,EAAEA,sBAAsB,CAACN,aAAa;EAC9D,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA,SAASO,oBAAoBA,CAAA,EAAG;EAC9B;AACF;AACA;AACA;EACE,IAAIC,cAAc;;EAElB;AACF;AACA;AACA;AACA;EACE,SAASC,aAAaA,CAACC,QAAQ,EAAE;IAC/B,IAAI,OAAOF,cAAc,KAAK,WAAW,EAAE;MACzCA,cAAc,GAAGG,UAAU,CAAC,YAAY;QACtCH,cAAc,GAAGI,SAAS;QAC1B1B,OAAO,CAAC2B,mBAAmB,CAAC,CAAC;QAC7BH,QAAQ,CAAC,CAAC;MACZ,CAAC,EAAE,EAAE,CAAC;IACR;EACF;EAEA,OAAOD,aAAa;AACtB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASH,sBAAsBA,CAACN,aAAa,EAAE;EAC7C,IAAId,OAAO,CAAC4B,qBAAqB,CAACd,aAAa,CAAC,EAAE;IAChD,OAAO,IAAI;EACb;EACA,IAAIA,aAAa,KAAKY,SAAS,IAAIZ,aAAa,KAAK,IAAI,IAAI,OAAOA,aAAa,KAAK,QAAQ,EAAE;IAC9F;IACA,OAAO,KAAK;EACd;EAEA,IAAIe,UAAU,GAAG,KAAK;EACtB,IAAIC,uBAAuB,GAAG,IAAI;EAClC,KAAK,IAAIZ,GAAG,IAAIJ,aAAa,EAAE;IAC7Be,UAAU,GAAG,IAAI;;IAEjB;IACA,IAAIX,GAAG,KAAK,YAAY,EAAE;MACxB;IACF;;IAEA;IACA;IACA;IACA;IACA,IAAIa,WAAW,GAAGjB,aAAa,CAACI,GAAG,CAAC;IACpC,IAAI,CAAClB,OAAO,CAAC4B,qBAAqB,CAACG,WAAW,CAAC,EAAE;MAC/CD,uBAAuB,GAAG,KAAK;IACjC;EACF;EAEA,OAAOD,UAAU,IAAIC,uBAAuB;AAC9C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,8BAA8BA,CAAClB,aAAa,EAAEX,QAAQ,EAAE;EAC/D,IAAIH,OAAO,CAAC4B,qBAAqB,CAACd,aAAa,CAAC,EAAE;IAChD;IACAd,OAAO,CAACiC,QAAQ,CAACnB,aAAa,EAAEX,QAAQ,GAAG,YAAY,CAAC;EAC1D;EAEA,IAAIW,aAAa,KAAKY,SAAS,IAAIZ,aAAa,KAAK,IAAI,IAAI,OAAOA,aAAa,KAAK,QAAQ,EAAE;IAC9F;IACA;EACF;EAEA,KAAK,IAAII,GAAG,IAAIJ,aAAa,EAAE;IAC7B;IACA,IAAII,GAAG,KAAK,YAAY,EAAE;MACxB;IACF;IAEA,IAAIa,WAAW,GAAGjB,aAAa,CAACI,GAAG,CAAC;IACpC,IAAIlB,OAAO,CAAC4B,qBAAqB,CAACG,WAAW,CAAC,EAAE;MAC9C,IAAIG,MAAM,GAAG/B,QAAQ,GAAG,aAAa,GAAGe,GAAG;MAC3ClB,OAAO,CAACiC,QAAQ,CAACF,WAAW,EAAEG,MAAM,CAAC;IACvC;EACF;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,oCAAoCA,CAACC,aAAa,EAAEC,aAAa,EAAE;EAC1E,IAAID,aAAa,CAACE,MAAM,KAAKD,aAAa,CAACC,MAAM,EAAE;IACjD,OAAO,IAAI;EACb;EAEA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,aAAa,CAACC,MAAM,EAAEC,CAAC,IAAI,CAAC,EAAE;IAChD,IAAIH,aAAa,CAACG,CAAC,CAAC,KAAKF,aAAa,CAACE,CAAC,CAAC,EAAE;MACzC,OAAO,IAAI;IACb;EACF;EAEA,OAAO,KAAK;AACd;AAEA,IAAIhB,aAAa,GAAGF,oBAAoB,CAAC,CAAC;AAC1C,SAASmB,cAAcA,CAAC1B,aAAa,EAAEX,QAAQ,EAAEsC,UAAU,EAAEC,cAAc,EAAEC,MAAM,EAAE;EACnFX,8BAA8B,CAAClB,aAAa,EAAEX,QAAQ,CAAC;EAEvD,IAAIsC,UAAU,EAAE;IACd,IAAIG,WAAW,GAAG,CAAC,CAACH,UAAU,CAACI,IAAI;IACnC,IAAIC,QAAQ;IACZ,IAAIF,WAAW,EAAE;MACfE,QAAQ,GAAGL,UAAU,CAACI,IAAI,CAACC,QAAQ;IACrC;IAEA,IAAI1B,sBAAsB,CAACN,aAAa,CAAC,EAAE;MACzC2B,UAAU,CAACM,OAAO;MAChB;AACR;AACA;AACA;AACA;AACA;MACQ,SAASC,kBAAkBA,CAACH,IAAI,EAAE;QAChC;QACAA,IAAI,CAACC,QAAQ,GAAG3B,iBAAiB,CAACL,aAAa,CAAC;MAClD,CACF,CAAC;MACD2B,UAAU,CAACQ,MAAM;MACf;AACR;AACA;AACA;AACA;MACQ,SAASC,eAAeA,CAACC,KAAK,EAAE;QAC9B,IAAI,OAAOT,cAAc,KAAK,WAAW,IAAIA,cAAc,EAAE;UAC3DA,cAAc,CAACU,kBAAkB,CAACD,KAAK,CAAC;QAC1C;QAEA,IAAI,OAAOR,MAAM,KAAK,WAAW,IAAIA,MAAM,EAAE;UAC3C,IAAIU,MAAM,CAACC,gBAAgB,EAAE;YAC3BD,MAAM,CAACC,gBAAgB,CAACH,KAAK,CAACI,OAAO,CAAC;UACxC;QACF;QAEAlD,mBAAmB,CAACC,CAAC,CAACH,QAAQ,CAAC,CAACqD,GAAG,CAACP,MAAM,CAACC,eAAe,CAAC;MAC7D,CACF,CAAC;MAED,IAAIN,WAAW,EAAE;QACf,IACEE,QAAQ,IACRA,QAAQ,CAAC1B,sBAAsB,IAC/Be,oCAAoC,CAClCW,QAAQ,CAAC/B,SAAS,EAClBF,gCAAgC,CAACC,aAAa,CAChD,CAAC,EACD;UACA2B,UAAU,CAACgB,UAAU,CAAC,CAAC;QACzB,CAAC,MAAM;UACLlC,aAAa;UACX;AACZ;AACA;AACA;UACY,SAASmC,cAAcA,CAAA,EAAG;YACxB,IAAI,OAAOhB,cAAc,KAAK,WAAW,IAAIA,cAAc,EAAE;cAC3DA,cAAc,CAACiB,kBAAkB,CAAC,CAAC;YACrC;UACF,CACF,CAAC;QACH;MACF;IACF,CAAC,MAAM;MACL,IAAIf,WAAW,IAAI,OAAOE,QAAQ,KAAK,WAAW,EAAE;QAClDL,UAAU,CAACgB,UAAU,CAAC,CAAC;MACzB;IACF;EACF;AACF;AAEAG,MAAM,CAAClD,OAAO,GAAGmD,MAAM,CAACC,MAAM,CAAC;EAC7BvC,aAAa,EAAEA,aAAa;EAC5BiB,cAAc,EAAEA,cAAc;EAC9BtC,gBAAgB,EAAEA,gBAAgB;EAClCkB,sBAAsB,EAAEA,sBAAsB;EAC9CY,8BAA8B,EAAEA;AAClC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}