// User types
export interface User {
  id: string;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  fullName: string;
  bio: string;
  role: 'user' | 'admin';
  avatar: string;
  socialLinks: {
    twitter: string;
    linkedin: string;
    github: string;
    website: string;
  };
  isActive: boolean;
  lastLogin: string | null;
  createdAt: string;
  updatedAt: string;
}

// Blog types
export interface Blog {
  _id: string;
  title: string;
  slug: string;
  content: string;
  excerpt: string;
  featuredImage: string;
  author: User;
  category: BlogCategory;
  tags: string[];
  status: 'draft' | 'published' | 'archived';
  publishedAt: string | null;
  views: number;
  likes: BlogLike[];
  likeCount: number;
  commentCount: number;
  readTime: number;
  seo: {
    metaTitle: string;
    metaDescription: string;
    keywords: string[];
  };
  isLiked?: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface BlogLike {
  user: string;
  likedAt: string;
}

export type BlogCategory = 
  | 'Technology'
  | 'Lifestyle'
  | 'Travel'
  | 'Food'
  | 'Health'
  | 'Business'
  | 'Education'
  | 'Entertainment'
  | 'Sports'
  | 'Politics'
  | 'Science'
  | 'Other';

// Comment types
export interface Comment {
  _id: string;
  content: string;
  author: User;
  blog: string;
  parentComment: string | null;
  replies: Comment[];
  likes: CommentLike[];
  likeCount: number;
  replyCount: number;
  isEdited: boolean;
  editedAt: string | null;
  status: 'active' | 'hidden' | 'deleted';
  createdAt: string;
  updatedAt: string;
}

export interface CommentLike {
  user: string;
  likedAt: string;
}

// API Response types
export interface ApiResponse<T = any> {
  message: string;
  data?: T;
  error?: string;
}

export interface PaginationMeta {
  currentPage: number;
  totalPages: number;
  totalItems?: number;
  totalBlogs?: number;
  totalComments?: number;
  totalUsers?: number;
  hasNext: boolean;
  hasPrev: boolean;
  nextPage?: number | null;
  prevPage?: number | null;
}

export interface BlogsResponse {
  blogs: Blog[];
  pagination: PaginationMeta;
}

export interface CommentsResponse {
  comments: Comment[];
  pagination: PaginationMeta;
}

export interface UsersResponse {
  users: User[];
  pagination: PaginationMeta;
}

// Auth types
export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  username: string;
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  bio?: string;
}

export interface AuthResponse {
  message: string;
  token: string;
  user: User;
}

// Form types
export interface BlogFormData {
  title: string;
  content: string;
  excerpt?: string;
  category: BlogCategory;
  tags: string[];
  status: 'draft' | 'published';
  featuredImage?: string;
  seo?: {
    metaTitle?: string;
    metaDescription?: string;
    keywords?: string[];
  };
}

export interface CommentFormData {
  content: string;
  blog: string;
  parentComment?: string;
}

export interface ProfileUpdateData {
  firstName: string;
  lastName: string;
  bio: string;
  avatar?: string;
  socialLinks: {
    twitter: string;
    linkedin: string;
    github: string;
    website: string;
  };
}

export interface PasswordChangeData {
  currentPassword: string;
  newPassword: string;
}

// Search and Filter types
export interface BlogFilters {
  category?: BlogCategory;
  tag?: string;
  author?: string;
  search?: string;
  sortBy?: 'publishedAt' | 'views' | 'likeCount' | 'createdAt';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

export interface UserFilters {
  search?: string;
  role?: 'user' | 'admin';
  isActive?: boolean;
  page?: number;
  limit?: number;
}

// Statistics types
export interface PlatformStats {
  users: {
    total: number;
    active: number;
    recent: number;
  };
  blogs: {
    total: number;
    published: number;
    draft: number;
    recent: number;
  };
  comments: {
    total: number;
  };
}

// Context types
export interface AuthContextType {
  user: User | null;
  token: string | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (credentials: LoginCredentials) => Promise<void>;
  register: (data: RegisterData) => Promise<void>;
  logout: () => void;
  updateProfile: (data: ProfileUpdateData) => Promise<void>;
  changePassword: (data: PasswordChangeData) => Promise<void>;
}

export interface BlogContextType {
  blogs: Blog[];
  currentBlog: Blog | null;
  isLoading: boolean;
  error: string | null;
  pagination: PaginationMeta | null;
  fetchBlogs: (filters?: BlogFilters) => Promise<void>;
  fetchBlogBySlug: (slug: string) => Promise<void>;
  createBlog: (data: BlogFormData) => Promise<Blog>;
  updateBlog: (id: string, data: Partial<BlogFormData>) => Promise<Blog>;
  deleteBlog: (id: string) => Promise<void>;
  likeBlog: (id: string) => Promise<void>;
  clearCurrentBlog: () => void;
}

// Error types
export interface ApiError {
  message: string;
  errors?: Array<{
    field: string;
    message: string;
  }>;
  status?: number;
}
